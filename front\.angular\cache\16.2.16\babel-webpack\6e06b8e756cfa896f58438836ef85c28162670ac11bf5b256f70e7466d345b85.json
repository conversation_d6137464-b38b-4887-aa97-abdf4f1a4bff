{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { FlightClass } from '../../models/flight-search.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/flight.service\";\nimport * as i3 from \"../../services/flight-config.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction SearchFlightComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 73);\n    i0.ɵɵelement(2, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SearchFlightComponent_div_18_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.clearError());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 76);\n    i0.ɵɵelement(7, \"path\", 77);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction SearchFlightComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"departureLocation\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"arrivalLocation\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getErrorMessage(\"departureDate\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_60_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getErrorMessage(\"returnDate\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"label\", 36);\n    i0.ɵɵtext(2, \"To\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 79);\n    i0.ɵɵtemplate(4, SearchFlightComponent_div_60_div_4_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"error\", ctx_r4.hasError(\"returnDate\", \"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.formControls[\"returnDate\"].touched && ctx_r4.formControls[\"returnDate\"].errors);\n  }\n}\nfunction SearchFlightComponent_option_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", count_r15);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(count_r15);\n  }\n}\nfunction SearchFlightComponent_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", count_r16);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(count_r16);\n  }\n}\nfunction SearchFlightComponent_option_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", count_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(count_r17);\n  }\n}\nfunction SearchFlightComponent_option_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r18.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r18.label);\n  }\n}\nfunction SearchFlightComponent_option_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const airline_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", airline_r19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(airline_r19);\n  }\n}\nfunction SearchFlightComponent_span_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"SEARCH NOW\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchFlightComponent_span_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 82);\n    i0.ɵɵelement(2, \"circle\", 83)(3, \"path\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Searching... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SearchFlightComponent {\n  constructor(formBuilder, flightService, flightConfigService, router) {\n    this.formBuilder = formBuilder;\n    this.flightService = flightService;\n    this.flightConfigService = flightConfigService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    // Options pour les sélecteurs\n    this.flightClasses = [{\n      value: FlightClass.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClass.PREMIUM_ECONOMY,\n      label: 'Premium Economy'\n    }, {\n      value: FlightClass.BUSINESS,\n      label: 'Business'\n    }, {\n      value: FlightClass.FIRST,\n      label: 'First Class'\n    }];\n    // Données pour les passagers\n    this.passengerCounts = {\n      adults: Array.from({\n        length: 9\n      }, (_, i) => i + 1),\n      children: Array.from({\n        length: 8\n      }, (_, i) => i),\n      infants: Array.from({\n        length: 4\n      }, (_, i) => i)\n    };\n    // Compagnies aériennes préférées (exemple)\n    this.preferredAirlines = ['Preferred Airline', 'Turkish Airlines', 'Emirates', 'Qatar Airways', 'Lufthansa', 'Air France', 'British Airways'];\n  }\n  ngOnInit() {\n    // Initialiser la configuration\n    this.initializeConfig();\n    this.initializeForm();\n  }\n  /**\n   * Initialise la configuration du composant\n   * PERSONNALISEZ CETTE MÉTHODE SELON VOS MODÈLES BACKEND\n   */\n  initializeConfig() {\n    // Option 1: Configuration par défaut pour l'API Paximum\n    this.flightConfigService.configureForPaximumAPI();\n    // Option 2: Configuration personnalisée pour vos modèles exacts\n    // Décommentez et adaptez selon vos besoins :\n    /*\n    this.flightConfigService.configureForBackend({\n      oneWayFields: {\n        productType: 'VotreChampProductType',\n        serviceTypes: 'VotreChampServiceTypes',\n        checkIn: 'VotreChampDateDepart',\n        departureLocations: 'VotreChampAeroportDepart',\n        arrivalLocations: 'VotreChampAeroportArrivee',\n        passengers: 'VotreChampPassagers',\n        showOnlyNonStopFlight: 'VotreChampVolsDirects',\n        culture: 'VotreChampCulture',\n        currency: 'VotreChampDevise'\n      },\n      defaults: {\n        productTypeValue: 2, // Votre valeur pour les vols\n        serviceTypesValue: ['1'], // Vos types de service\n        culture: 'fr-FR', // Votre culture par défaut\n        currency: 'EUR' // Votre devise par défaut\n      },\n      endpoints: {\n        oneWay: '/api/votre-endpoint/aller-simple',\n        roundTrip: '/api/votre-endpoint/aller-retour',\n        multiCity: '/api/votre-endpoint/multi-destinations'\n      }\n    });\n    */\n    this.config = this.flightConfigService.getConfig();\n  }\n  /**\n   * Initialise le formulaire de recherche\n   */\n  initializeForm() {\n    this.searchForm = this.formBuilder.group({\n      tripType: ['oneWay', Validators.required],\n      departureLocation: ['', [Validators.required, Validators.minLength(3)]],\n      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.required, Validators.min(0)]],\n      infants: [0, [Validators.required, Validators.min(0)]],\n      flightClass: [FlightClass.ECONOMY, Validators.required],\n      directFlightsOnly: [false],\n      preferredAirline: ['']\n    });\n    // Validation conditionnelle pour la date de retour\n    this.searchForm.get('tripType')?.valueChanges.subscribe(tripType => {\n      const returnDateControl = this.searchForm.get('returnDate');\n      if (tripType === 'roundTrip') {\n        returnDateControl?.setValidators([Validators.required]);\n      } else {\n        returnDateControl?.clearValidators();\n      }\n      returnDateControl?.updateValueAndValidity();\n    });\n  }\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.searchForm.controls;\n  }\n  /**\n   * Échange les aéroports de départ et d'arrivée\n   */\n  swapAirports() {\n    const departure = this.searchForm.get('departureLocation')?.value;\n    const arrival = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrival,\n      arrivalLocation: departure\n    });\n  }\n  /**\n   * Obtient le nombre total de passagers\n   */\n  getTotalPassengers() {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n  /**\n   * Obtient le texte d'affichage pour les passagers\n   */\n  getPassengerText() {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;\n    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;\n    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;\n    return text;\n  }\n  /**\n   * Obtient le nombre de jours entre les dates\n   */\n  getDaysBetweenDates() {\n    const departureDate = this.searchForm.get('departureDate')?.value;\n    const returnDate = this.searchForm.get('returnDate')?.value;\n    if (!departureDate || !returnDate) return '';\n    const start = new Date(departureDate);\n    const end = new Date(returnDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return `+/- ${diffDays} Days`;\n  }\n  /**\n   * Soumet le formulaire de recherche\n   */\n  onSubmit() {\n    if (this.searchForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      const formData = {\n        tripType: this.searchForm.value.tripType,\n        departureLocation: this.searchForm.value.departureLocation.trim(),\n        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),\n        departureDate: this.searchForm.value.departureDate,\n        returnDate: this.searchForm.value.returnDate,\n        passengers: {\n          adults: this.searchForm.value.adults,\n          children: this.searchForm.value.children,\n          infants: this.searchForm.value.infants\n        },\n        flightClass: this.searchForm.value.flightClass,\n        directFlightsOnly: this.searchForm.value.directFlightsOnly,\n        preferredAirline: this.searchForm.value.preferredAirline\n      };\n      try {\n        // Mapper les données du formulaire vers le format backend\n        const backendRequest = this.flightConfigService.mapFormToBackend(formData, formData.tripType);\n        const endpoint = this.flightConfigService.getEndpoint(formData.tripType);\n        console.log('Requête backend générée:', backendRequest);\n        console.log('Endpoint utilisé:', endpoint);\n        // Appel du service avec les données mappées\n        let searchObservable;\n        switch (formData.tripType) {\n          case 'oneWay':\n            searchObservable = this.flightService.searchOneWayFlightsWithCustomData(backendRequest, endpoint);\n            break;\n          case 'roundTrip':\n            searchObservable = this.flightService.searchRoundTripFlightsWithCustomData(backendRequest, endpoint);\n            break;\n          case 'multiCity':\n            searchObservable = this.flightService.searchMulticityFlightsWithCustomData(backendRequest, endpoint);\n            break;\n          default:\n            this.isLoading = false;\n            this.errorMessage = 'Type de voyage non valide';\n            return;\n        }\n        searchObservable.subscribe({\n          next: response => {\n            this.isLoading = false;\n            if (response.header && response.header.success) {\n              console.log('Recherche réussie:', response);\n              // Rediriger vers la page de résultats ou traiter les résultats\n              // this.router.navigate(['/flight-results'], { state: { results: response } });\n            } else {\n              this.handleApiError(response);\n            }\n          },\n          error: error => {\n            this.isLoading = false;\n            this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';\n            console.error('Erreur de recherche:', error);\n          }\n        });\n      } catch (error) {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Erreur de configuration';\n        console.error('Erreur de mapping:', error);\n      }\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Aucun vol trouvé pour ces critères';\n    }\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.searchForm.controls).forEach(key => {\n      const control = this.searchForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName, errorType) {\n    const field = this.searchForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.searchForm.get(fieldName);\n    if (field?.hasError('required')) {\n      return `Ce champ est requis`;\n    }\n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `Minimum ${requiredLength} caractères requis`;\n    }\n    if (field?.hasError('min')) {\n      const min = field.errors?.['min']?.min;\n      return `La valeur minimum est ${min}`;\n    }\n    return '';\n  }\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function SearchFlightComponent_Factory(t) {\n      return new (t || SearchFlightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FlightService), i0.ɵɵdirectiveInject(i3.FlightConfigService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchFlightComponent,\n      selectors: [[\"app-search-flight\"]],\n      decls: 119,\n      vars: 29,\n      consts: [[1, \"search-flight-container\"], [1, \"search-header\"], [1, \"search-title-section\"], [1, \"search-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\"], [1, \"search-title-content\"], [1, \"search-title\"], [1, \"search-subtitle\"], [1, \"latest-searches\"], [1, \"latest-title\"], [1, \"latest-subtitle\"], [1, \"search-content\"], [1, \"search-form-container\"], [\"class\", \"error-message\", \"role\", \"alert\", 4, \"ngIf\"], [\"novalidate\", \"\", 1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-tabs\"], [\"type\", \"button\", 1, \"tab-button\", 3, \"click\"], [1, \"location-row\"], [1, \"location-field\"], [1, \"location-label\"], [1, \"location-input-container\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"location-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"], [\"type\", \"text\", \"formControlName\", \"departureLocation\", \"placeholder\", \"IST - Istanbul Airport\", 1, \"location-input\"], [\"type\", \"button\", 1, \"location-button\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"swap-button\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"type\", \"text\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"TUN - Carthage Arpt\", 1, \"location-input\"], [1, \"date-row\"], [1, \"date-field\"], [1, \"date-label\"], [\"type\", \"date\", \"formControlName\", \"departureDate\", 1, \"date-input\"], [\"class\", \"date-field\", 4, \"ngIf\"], [1, \"passenger-class-row\"], [1, \"passenger-field\"], [1, \"passenger-label\"], [1, \"passenger-display\"], [1, \"passenger-icons\"], [1, \"passenger-group\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"passenger-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"formControlName\", \"adults\", 1, \"passenger-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"formControlName\", \"children\", 1, \"passenger-select\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"], [\"formControlName\", \"infants\", 1, \"passenger-select\"], [1, \"class-group\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"class-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"formControlName\", \"flightClass\", 1, \"class-select\"], [1, \"airline-field\"], [1, \"airline-label\"], [\"formControlName\", \"preferredAirline\", 1, \"airline-select\"], [\"value\", \"\"], [1, \"options-row\"], [1, \"option-group\"], [1, \"option-label\"], [1, \"option-select\"], [1, \"calendar-display\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"latest-searches-sidebar\"], [1, \"latest-searches-content\"], [1, \"no-searches\"], [\"role\", \"alert\", 1, \"error-message\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"error-icon\"], [\"fill-rule\", \"evenodd\", \"d\", \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [\"type\", \"button\", 1, \"error-close\", 3, \"click\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\", \"clip-rule\", \"evenodd\"], [1, \"field-error\"], [\"type\", \"date\", \"formControlName\", \"returnDate\", 1, \"date-input\"], [3, \"value\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 24 24\", 1, \"loading-spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", \"fill\", \"none\", \"opacity\", \"0.25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", \"opacity\", \"0.75\"]],\n      template: function SearchFlightComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"circle\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"h1\", 7);\n          i0.ɵɵtext(8, \"Search and Book Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 8);\n          i0.ɵɵtext(10, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"h2\", 10);\n          i0.ɵɵtext(13, \"Latest Searches\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\", 11);\n          i0.ɵɵtext(15, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13);\n          i0.ɵɵtemplate(18, SearchFlightComponent_div_18_Template, 8, 1, \"div\", 14);\n          i0.ɵɵelementStart(19, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchFlightComponent_Template_form_ngSubmit_19_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_21_listener() {\n            return ctx.searchForm.patchValue({\n              tripType: \"oneWay\"\n            });\n          });\n          i0.ɵɵtext(22, \" One way \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_23_listener() {\n            return ctx.searchForm.patchValue({\n              tripType: \"roundTrip\"\n            });\n          });\n          i0.ɵɵtext(24, \" Round Trip \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_25_listener() {\n            return ctx.searchForm.patchValue({\n              tripType: \"multiCity\"\n            });\n          });\n          i0.ɵɵtext(26, \" Multi-City/Stop-Overs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 19)(29, \"label\", 20);\n          i0.ɵɵtext(30, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 21);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(32, \"svg\", 22);\n          i0.ɵɵelement(33, \"path\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(34, \"input\", 24);\n          i0.ɵɵelementStart(35, \"button\", 25);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(36, \"svg\", 26);\n          i0.ɵɵelement(37, \"path\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(38, SearchFlightComponent_div_38_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(39, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_39_listener() {\n            return ctx.swapAirports();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(40, \"svg\", 26);\n          i0.ɵɵelement(41, \"path\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(42, \"div\", 19)(43, \"label\", 20);\n          i0.ɵɵtext(44, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 21);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(46, \"svg\", 22);\n          i0.ɵɵelement(47, \"path\", 31)(48, \"path\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(49, \"input\", 33);\n          i0.ɵɵelementStart(50, \"button\", 25);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(51, \"svg\", 26);\n          i0.ɵɵelement(52, \"path\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(53, SearchFlightComponent_div_53_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(54, \"div\", 34)(55, \"div\", 35)(56, \"label\", 36);\n          i0.ɵɵtext(57, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"input\", 37);\n          i0.ɵɵtemplate(59, SearchFlightComponent_div_59_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, SearchFlightComponent_div_60_Template, 5, 3, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 39)(62, \"div\", 40)(63, \"label\", 41);\n          i0.ɵɵtext(64, \"Passenger & Class of travel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 42)(66, \"div\", 43)(67, \"div\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(68, \"svg\", 45);\n          i0.ɵɵelement(69, \"path\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(70, \"select\", 47);\n          i0.ɵɵtemplate(71, SearchFlightComponent_option_71_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 45);\n          i0.ɵɵelement(74, \"path\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"select\", 50);\n          i0.ɵɵtemplate(76, SearchFlightComponent_option_76_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(78, \"svg\", 45);\n          i0.ɵɵelement(79, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(80, \"select\", 52);\n          i0.ɵɵtemplate(81, SearchFlightComponent_option_81_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 53);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(83, \"svg\", 54);\n          i0.ɵɵelement(84, \"path\", 55);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(85, \"select\", 56);\n          i0.ɵɵtemplate(86, SearchFlightComponent_option_86_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(87, \"div\", 57)(88, \"label\", 58);\n          i0.ɵɵtext(89, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"select\", 59)(91, \"option\", 60);\n          i0.ɵɵtext(92, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(93, SearchFlightComponent_option_93_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 61)(95, \"div\", 62)(96, \"label\", 63);\n          i0.ɵɵtext(97, \"Refundable fares\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"select\", 64)(99, \"option\");\n          i0.ɵɵtext(100, \"--All--\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 62)(102, \"label\", 63);\n          i0.ɵɵtext(103, \"Baggage\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"select\", 64)(105, \"option\");\n          i0.ɵɵtext(106, \"--All--\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"div\", 62)(108, \"label\", 63);\n          i0.ɵɵtext(109, \"Calendar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 65);\n          i0.ɵɵtext(111);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(112, \"button\", 66);\n          i0.ɵɵtemplate(113, SearchFlightComponent_span_113_Template, 2, 0, \"span\", 67);\n          i0.ɵɵtemplate(114, SearchFlightComponent_span_114_Template, 5, 0, \"span\", 68);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(115, \"div\", 69)(116, \"div\", 70)(117, \"p\", 71);\n          i0.ɵɵtext(118, \"No recent searches\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.formControls[\"tripType\"].value === \"oneWay\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.formControls[\"tripType\"].value === \"roundTrip\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.formControls[\"tripType\"].value === \"multiCity\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"departureLocation\", \"required\") || ctx.hasError(\"departureLocation\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"departureLocation\"].touched && ctx.formControls[\"departureLocation\"].errors);\n          i0.ɵɵadvance(11);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"arrivalLocation\", \"required\") || ctx.hasError(\"arrivalLocation\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"arrivalLocation\"].touched && ctx.formControls[\"arrivalLocation\"].errors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"departureDate\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"departureDate\"].touched && ctx.formControls[\"departureDate\"].errors);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"tripType\"].value === \"roundTrip\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerCounts.adults);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerCounts.children);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerCounts.infants);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.preferredAirlines);\n          i0.ɵɵadvance(18);\n          i0.ɵɵtextInterpolate(ctx.getDaysBetweenDates() || \"+/- 3 Days\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.searchForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n[_nghost-%COMP%] {\\n  --primary-blue: #4a6fa5;\\n  --secondary-blue: #7bb3f0;\\n  --dark-blue: #2c4a7a;\\n  --light-blue: #e8f4fd;\\n  --white: #ffffff;\\n  --light-gray: #f8f9fa;\\n  --medium-gray: #6c757d;\\n  --dark-gray: #343a40;\\n  --border-color: #dee2e6;\\n  --success-color: #28a745;\\n  --error-color: #dc3545;\\n  --error-bg: #f8d7da;\\n  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);\\n  --border-radius: 8px;\\n  --transition: all 0.3s ease;\\n}\\n\\n\\n\\n.search-flight-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n  padding: 2rem;\\n}\\n\\n\\n\\n.search-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 2rem;\\n  gap: 2rem;\\n  background: var(--white);\\n  padding: 1.5rem 2rem;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--shadow);\\n}\\n\\n.search-title-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n  background: var(--primary-blue);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--white);\\n  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.3);\\n}\\n\\n.search-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.search-title-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin: 0 0 0.25rem 0;\\n}\\n\\n.search-title-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--medium-gray);\\n  margin: 0;\\n}\\n\\n.latest-searches[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.latest-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin: 0 0 0.25rem 0;\\n}\\n\\n.latest-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--medium-gray);\\n  margin: 0;\\n}\\n\\n\\n\\n.search-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 2rem;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.search-form-container[_ngcontent-%COMP%] {\\n  background: var(--white);\\n  border-radius: var(--border-radius);\\n  padding: 2rem;\\n  box-shadow: var(--shadow);\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background-color: var(--error-bg);\\n  border: 1px solid #feb2b2;\\n  color: var(--error-color);\\n  padding: 1rem;\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  flex-shrink: 0;\\n  margin-top: 0.125rem;\\n}\\n\\n.error-close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.75rem;\\n  right: 0.75rem;\\n  background: none;\\n  border: none;\\n  color: var(--error-color);\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.error-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(229, 62, 62, 0.1);\\n}\\n\\n.error-close[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n\\n\\n.trip-type-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 2rem;\\n  border-radius: var(--border-radius);\\n  overflow: hidden;\\n  background: var(--light-gray);\\n  border: 1px solid var(--border-color);\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem 1.5rem;\\n  background: transparent;\\n  border: none;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--medium-gray);\\n  cursor: pointer;\\n  transition: var(--transition);\\n  position: relative;\\n}\\n\\n.tab-button.active[_ngcontent-%COMP%] {\\n  background: var(--dark-blue);\\n  color: var(--white);\\n  font-weight: 600;\\n}\\n\\n.tab-button[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: rgba(74, 111, 165, 0.1);\\n  color: var(--primary-blue);\\n}\\n\\n\\n\\n.location-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.location-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.location-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.location-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: var(--white);\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  transition: var(--transition);\\n}\\n\\n.location-input-container[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.location-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--primary-blue);\\n  z-index: 1;\\n}\\n\\n.location-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem 3rem 1rem 3rem;\\n  border: none;\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: transparent;\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.location-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n.location-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n}\\n\\n.location-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0.5rem;\\n  background: none;\\n  border: none;\\n  padding: 0.5rem;\\n  cursor: pointer;\\n  color: var(--medium-gray);\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.location-button[_ngcontent-%COMP%]:hover {\\n  background: var(--light-gray);\\n  color: var(--primary-blue);\\n}\\n\\n.location-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n\\n\\n.swap-button[_ngcontent-%COMP%] {\\n  background: var(--white);\\n  border: 2px solid var(--primary-blue);\\n  border-radius: 50%;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--primary-blue);\\n  cursor: pointer;\\n  transition: var(--transition);\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.2);\\n}\\n\\n.swap-button[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-blue);\\n  color: var(--white);\\n  transform: rotate(180deg);\\n  box-shadow: 0 4px 12px rgba(74, 111, 165, 0.3);\\n}\\n\\n.swap-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n\\n\\n.date-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.date-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.date-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.date-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.date-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.date-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n}\\n\\n\\n\\n.passenger-class-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.passenger-field[_ngcontent-%COMP%] {\\n  flex: 2;\\n}\\n\\n.passenger-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.passenger-display[_ngcontent-%COMP%] {\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  padding: 1rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n}\\n\\n.passenger-display[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.passenger-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n  flex-wrap: wrap;\\n}\\n\\n.passenger-group[_ngcontent-%COMP%], .class-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.passenger-icon[_ngcontent-%COMP%], .class-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--primary-blue);\\n}\\n\\n.passenger-select[_ngcontent-%COMP%], .class-select[_ngcontent-%COMP%] {\\n  border: 1px solid var(--border-color);\\n  border-radius: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  min-width: 3.5rem;\\n  font-weight: 500;\\n  transition: var(--transition);\\n}\\n\\n.passenger-select[_ngcontent-%COMP%]:focus, .class-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n}\\n\\n\\n\\n.airline-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.airline-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.airline-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.airline-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n\\n\\n.options-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.option-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.option-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.option-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.option-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.calendar-display[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--light-gray);\\n  color: var(--primary-blue);\\n  text-align: center;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.search-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);\\n  color: var(--white);\\n  border: none;\\n  padding: 1.25rem 2rem;\\n  border-radius: var(--border-radius);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  min-height: 3.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);\\n}\\n\\n.search-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n\\n\\n.latest-searches-sidebar[_ngcontent-%COMP%] {\\n  background: var(--white);\\n  border-radius: var(--border-radius);\\n  padding: 2rem;\\n  box-shadow: var(--shadow);\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  border: 1px solid var(--border-color);\\n}\\n\\n.latest-searches-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 2rem 0;\\n}\\n\\n.no-searches[_ngcontent-%COMP%] {\\n  color: var(--medium-gray);\\n  font-style: italic;\\n  font-size: 0.875rem;\\n}\\n\\n\\n\\n.field-error[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 1024px) {\\n  .search-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .latest-searches-sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .search-flight-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  \\n  .search-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  \\n  .location-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .swap-button[_ngcontent-%COMP%] {\\n    align-self: center;\\n    margin: 0.5rem 0;\\n  }\\n  \\n  .passenger-class-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .options-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .passenger-icons[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "FlightClass", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "SearchFlightComponent_div_18_Template_button_click_5_listener", "ɵɵrestoreView", "_r13", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r2", "ctx_r3", "ctx_r14", "ɵɵtemplate", "SearchFlightComponent_div_60_div_4_Template", "ɵɵclassProp", "ctx_r4", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵproperty", "formControls", "touched", "errors", "count_r15", "count_r16", "count_r17", "flightClass_r18", "value", "label", "airline_r19", "SearchFlightComponent", "constructor", "formBuilder", "flightService", "flightConfigService", "router", "isLoading", "flightClasses", "ECONOMY", "PREMIUM_ECONOMY", "BUSINESS", "FIRST", "passengerCounts", "adults", "Array", "from", "length", "_", "i", "children", "infants", "preferredAirlines", "ngOnInit", "initializeConfig", "initializeForm", "configureForPaximumAPI", "config", "getConfig", "searchForm", "group", "tripType", "required", "departureLocation", "<PERSON><PERSON><PERSON><PERSON>", "arrivalLocation", "departureDate", "returnDate", "min", "flightClass", "directFlightsOnly", "preferredAirline", "get", "valueChanges", "subscribe", "returnDateControl", "setValidators", "clearValidators", "updateValueAndValidity", "controls", "swapAirports", "departure", "arrival", "patchValue", "getTotalPassengers", "getPassengerText", "text", "getDaysBetweenDates", "start", "Date", "end", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "onSubmit", "valid", "formData", "trim", "passengers", "backendRequest", "mapFormToBackend", "endpoint", "getEndpoint", "console", "log", "searchObservable", "searchOneWayFlightsWithCustomData", "searchRoundTripFlightsWithCustomData", "searchMulticityFlightsWithCustomData", "next", "response", "header", "success", "handleApiError", "error", "message", "markFormGroupTouched", "messages", "Object", "keys", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errorType", "field", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "FlightService", "i3", "FlightConfigService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "SearchFlightComponent_Template", "rf", "ctx", "SearchFlightComponent_div_18_Template", "SearchFlightComponent_Template_form_ngSubmit_19_listener", "SearchFlightComponent_Template_button_click_21_listener", "SearchFlightComponent_Template_button_click_23_listener", "SearchFlightComponent_Template_button_click_25_listener", "SearchFlightComponent_div_38_Template", "SearchFlightComponent_Template_button_click_39_listener", "SearchFlightComponent_div_53_Template", "SearchFlightComponent_div_59_Template", "SearchFlightComponent_div_60_Template", "SearchFlightComponent_option_71_Template", "SearchFlightComponent_option_76_Template", "SearchFlightComponent_option_81_Template", "SearchFlightComponent_option_86_Template", "SearchFlightComponent_option_93_Template", "SearchFlightComponent_span_113_Template", "SearchFlightComponent_span_114_Template", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\search-flight\\search-flight.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\search-flight\\search-flight.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { FlightService } from '../../services/flight.service';\nimport { FlightConfigService } from '../../services/flight-config.service';\nimport { FlightSearchForm, FlightClass } from '../../models/flight-search.interface';\nimport { FlightSearchConfig } from '../../models/backend-field-mapping.interface';\n\n@Component({\n  selector: 'app-search-flight',\n  templateUrl: './search-flight.component.html',\n  styleUrls: ['./search-flight.component.css']\n})\nexport class SearchFlightComponent implements OnInit {\n  searchForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  config!: FlightSearchConfig;\n\n  // Options pour les sélecteurs\n  flightClasses = [\n    { value: FlightClass.ECONOMY, label: 'Economy' },\n    { value: FlightClass.PREMIUM_ECONOMY, label: 'Premium Economy' },\n    { value: FlightClass.BUSINESS, label: 'Business' },\n    { value: FlightClass.FIRST, label: 'First Class' }\n  ];\n\n  // Données pour les passagers\n  passengerCounts = {\n    adults: Array.from({length: 9}, (_, i) => i + 1),\n    children: Array.from({length: 8}, (_, i) => i),\n    infants: Array.from({length: 4}, (_, i) => i)\n  };\n\n  // Compagnies aériennes préférées (exemple)\n  preferredAirlines = [\n    'Preferred Airline',\n    'Turkish Airlines',\n    'Emirates',\n    'Qatar Airways',\n    'Lufthansa',\n    'Air France',\n    'British Airways'\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private flightService: FlightService,\n    private flightConfigService: FlightConfigService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Initialiser la configuration\n    this.initializeConfig();\n    this.initializeForm();\n  }\n\n  /**\n   * Initialise la configuration du composant\n   * PERSONNALISEZ CETTE MÉTHODE SELON VOS MODÈLES BACKEND\n   */\n  private initializeConfig(): void {\n    // Option 1: Configuration par défaut pour l'API Paximum\n    this.flightConfigService.configureForPaximumAPI();\n\n    // Option 2: Configuration personnalisée pour vos modèles exacts\n    // Décommentez et adaptez selon vos besoins :\n    /*\n    this.flightConfigService.configureForBackend({\n      oneWayFields: {\n        productType: 'VotreChampProductType',\n        serviceTypes: 'VotreChampServiceTypes',\n        checkIn: 'VotreChampDateDepart',\n        departureLocations: 'VotreChampAeroportDepart',\n        arrivalLocations: 'VotreChampAeroportArrivee',\n        passengers: 'VotreChampPassagers',\n        showOnlyNonStopFlight: 'VotreChampVolsDirects',\n        culture: 'VotreChampCulture',\n        currency: 'VotreChampDevise'\n      },\n      defaults: {\n        productTypeValue: 2, // Votre valeur pour les vols\n        serviceTypesValue: ['1'], // Vos types de service\n        culture: 'fr-FR', // Votre culture par défaut\n        currency: 'EUR' // Votre devise par défaut\n      },\n      endpoints: {\n        oneWay: '/api/votre-endpoint/aller-simple',\n        roundTrip: '/api/votre-endpoint/aller-retour',\n        multiCity: '/api/votre-endpoint/multi-destinations'\n      }\n    });\n    */\n\n    this.config = this.flightConfigService.getConfig();\n  }\n\n  /**\n   * Initialise le formulaire de recherche\n   */\n  private initializeForm(): void {\n    this.searchForm = this.formBuilder.group({\n      tripType: ['oneWay', Validators.required],\n      departureLocation: ['', [Validators.required, Validators.minLength(3)]],\n      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.required, Validators.min(0)]],\n      infants: [0, [Validators.required, Validators.min(0)]],\n      flightClass: [FlightClass.ECONOMY, Validators.required],\n      directFlightsOnly: [false],\n      preferredAirline: ['']\n    });\n\n    // Validation conditionnelle pour la date de retour\n    this.searchForm.get('tripType')?.valueChanges.subscribe(tripType => {\n      const returnDateControl = this.searchForm.get('returnDate');\n      if (tripType === 'roundTrip') {\n        returnDateControl?.setValidators([Validators.required]);\n      } else {\n        returnDateControl?.clearValidators();\n      }\n      returnDateControl?.updateValueAndValidity();\n    });\n  }\n\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.searchForm.controls;\n  }\n\n  /**\n   * Échange les aéroports de départ et d'arrivée\n   */\n  swapAirports(): void {\n    const departure = this.searchForm.get('departureLocation')?.value;\n    const arrival = this.searchForm.get('arrivalLocation')?.value;\n    \n    this.searchForm.patchValue({\n      departureLocation: arrival,\n      arrivalLocation: departure\n    });\n  }\n\n  /**\n   * Obtient le nombre total de passagers\n   */\n  getTotalPassengers(): number {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Obtient le texte d'affichage pour les passagers\n   */\n  getPassengerText(): string {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    \n    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;\n    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;\n    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;\n    \n    return text;\n  }\n\n  /**\n   * Obtient le nombre de jours entre les dates\n   */\n  getDaysBetweenDates(): string {\n    const departureDate = this.searchForm.get('departureDate')?.value;\n    const returnDate = this.searchForm.get('returnDate')?.value;\n    \n    if (!departureDate || !returnDate) return '';\n    \n    const start = new Date(departureDate);\n    const end = new Date(returnDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    return `+/- ${diffDays} Days`;\n  }\n\n  /**\n   * Soumet le formulaire de recherche\n   */\n  onSubmit(): void {\n    if (this.searchForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      const formData = {\n        tripType: this.searchForm.value.tripType,\n        departureLocation: this.searchForm.value.departureLocation.trim(),\n        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),\n        departureDate: this.searchForm.value.departureDate,\n        returnDate: this.searchForm.value.returnDate,\n        passengers: {\n          adults: this.searchForm.value.adults,\n          children: this.searchForm.value.children,\n          infants: this.searchForm.value.infants\n        },\n        flightClass: this.searchForm.value.flightClass,\n        directFlightsOnly: this.searchForm.value.directFlightsOnly,\n        preferredAirline: this.searchForm.value.preferredAirline\n      };\n\n      try {\n        // Mapper les données du formulaire vers le format backend\n        const backendRequest = this.flightConfigService.mapFormToBackend(formData, formData.tripType);\n        const endpoint = this.flightConfigService.getEndpoint(formData.tripType);\n\n        console.log('Requête backend générée:', backendRequest);\n        console.log('Endpoint utilisé:', endpoint);\n\n        // Appel du service avec les données mappées\n        let searchObservable;\n        switch (formData.tripType) {\n          case 'oneWay':\n            searchObservable = this.flightService.searchOneWayFlightsWithCustomData(backendRequest, endpoint);\n            break;\n          case 'roundTrip':\n            searchObservable = this.flightService.searchRoundTripFlightsWithCustomData(backendRequest, endpoint);\n            break;\n          case 'multiCity':\n            searchObservable = this.flightService.searchMulticityFlightsWithCustomData(backendRequest, endpoint);\n            break;\n          default:\n            this.isLoading = false;\n            this.errorMessage = 'Type de voyage non valide';\n            return;\n        }\n\n        searchObservable.subscribe({\n          next: (response) => {\n            this.isLoading = false;\n            if (response.header && response.header.success) {\n              console.log('Recherche réussie:', response);\n              // Rediriger vers la page de résultats ou traiter les résultats\n              // this.router.navigate(['/flight-results'], { state: { results: response } });\n            } else {\n              this.handleApiError(response);\n            }\n          },\n          error: (error) => {\n            this.isLoading = false;\n            this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';\n            console.error('Erreur de recherche:', error);\n          }\n        });\n      } catch (error: any) {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Erreur de configuration';\n        console.error('Erreur de mapping:', error);\n      }\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: any): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Aucun vol trouvé pour ces critères';\n    }\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.searchForm.controls).forEach(key => {\n      const control = this.searchForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.searchForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.searchForm.get(fieldName);\n    \n    if (field?.hasError('required')) {\n      return `Ce champ est requis`;\n    }\n    \n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `Minimum ${requiredLength} caractères requis`;\n    }\n    \n    if (field?.hasError('min')) {\n      const min = field.errors?.['min']?.min;\n      return `La valeur minimum est ${min}`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"search-flight-container\">\n  <!-- Header Section -->\n  <div class=\"search-header\">\n    <div class=\"search-title-section\">\n      <div class=\"search-icon\">\n        <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n        </svg>\n      </div>\n      <div class=\"search-title-content\">\n        <h1 class=\"search-title\">Search and Book Flights</h1>\n        <p class=\"search-subtitle\">We're bringing you a new level of comfort</p>\n      </div>\n    </div>\n    \n    <div class=\"latest-searches\">\n      <h2 class=\"latest-title\">Latest Searches</h2>\n      <p class=\"latest-subtitle\">We're bringing you a new level of comfort</p>\n    </div>\n  </div>\n\n  <!-- Main Search Form -->\n  <div class=\"search-content\">\n    <div class=\"search-form-container\">\n      <!-- Message d'erreur global -->\n      <div *ngIf=\"errorMessage\" class=\"error-message\" role=\"alert\">\n        <svg class=\"error-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">\n          <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n          </svg>\n        </button>\n      </div>\n\n      <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSubmit()\" class=\"search-form\" novalidate>\n        \n        <!-- Trip Type Tabs -->\n        <div class=\"trip-type-tabs\">\n          <button type=\"button\" \n                  class=\"tab-button\" \n                  [class.active]=\"formControls['tripType'].value === 'oneWay'\"\n                  (click)=\"searchForm.patchValue({tripType: 'oneWay'})\">\n            One way\n          </button>\n          <button type=\"button\" \n                  class=\"tab-button\" \n                  [class.active]=\"formControls['tripType'].value === 'roundTrip'\"\n                  (click)=\"searchForm.patchValue({tripType: 'roundTrip'})\">\n            Round Trip\n          </button>\n          <button type=\"button\" \n                  class=\"tab-button\" \n                  [class.active]=\"formControls['tripType'].value === 'multiCity'\"\n                  (click)=\"searchForm.patchValue({tripType: 'multiCity'})\">\n            Multi-City/Stop-Overs\n          </button>\n        </div>\n\n        <!-- Location Fields -->\n        <div class=\"location-row\">\n          <div class=\"location-field\">\n            <label class=\"location-label\">From</label>\n            <div class=\"location-input-container\">\n              <svg class=\"location-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"/>\n              </svg>\n              <input type=\"text\" \n                     formControlName=\"departureLocation\"\n                     class=\"location-input\"\n                     [class.error]=\"hasError('departureLocation', 'required') || hasError('departureLocation', 'minlength')\"\n                     placeholder=\"IST - Istanbul Airport\">\n              <button type=\"button\" class=\"location-button\">\n                <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>\n                </svg>\n              </button>\n            </div>\n            <div *ngIf=\"formControls['departureLocation'].touched && formControls['departureLocation'].errors\" class=\"field-error\">\n              {{ getErrorMessage('departureLocation') }}\n            </div>\n          </div>\n\n          <!-- Swap Button -->\n          <button type=\"button\" class=\"swap-button\" (click)=\"swapAirports()\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"/>\n            </svg>\n          </button>\n\n          <div class=\"location-field\">\n            <label class=\"location-label\">To</label>\n            <div class=\"location-input-container\">\n              <svg class=\"location-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\n              </svg>\n              <input type=\"text\" \n                     formControlName=\"arrivalLocation\"\n                     class=\"location-input\"\n                     [class.error]=\"hasError('arrivalLocation', 'required') || hasError('arrivalLocation', 'minlength')\"\n                     placeholder=\"TUN - Carthage Arpt\">\n              <button type=\"button\" class=\"location-button\">\n                <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>\n                </svg>\n              </button>\n            </div>\n            <div *ngIf=\"formControls['arrivalLocation'].touched && formControls['arrivalLocation'].errors\" class=\"field-error\">\n              {{ getErrorMessage('arrivalLocation') }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Date Field -->\n        <div class=\"date-row\">\n          <div class=\"date-field\">\n            <label class=\"date-label\">From</label>\n            <input type=\"date\" \n                   formControlName=\"departureDate\"\n                   class=\"date-input\"\n                   [class.error]=\"hasError('departureDate', 'required')\">\n            <div *ngIf=\"formControls['departureDate'].touched && formControls['departureDate'].errors\" class=\"field-error\">\n              {{ getErrorMessage('departureDate') }}\n            </div>\n          </div>\n\n          <div class=\"date-field\" *ngIf=\"formControls['tripType'].value === 'roundTrip'\">\n            <label class=\"date-label\">To</label>\n            <input type=\"date\" \n                   formControlName=\"returnDate\"\n                   class=\"date-input\"\n                   [class.error]=\"hasError('returnDate', 'required')\">\n            <div *ngIf=\"formControls['returnDate'].touched && formControls['returnDate'].errors\" class=\"field-error\">\n              {{ getErrorMessage('returnDate') }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Passenger & Class Row -->\n        <div class=\"passenger-class-row\">\n          <div class=\"passenger-field\">\n            <label class=\"passenger-label\">Passenger & Class of travel</label>\n            <div class=\"passenger-display\">\n              <div class=\"passenger-icons\">\n                <div class=\"passenger-group\">\n                  <svg class=\"passenger-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>\n                  </svg>\n                  <select formControlName=\"adults\" class=\"passenger-select\">\n                    <option *ngFor=\"let count of passengerCounts.adults\" [value]=\"count\">{{ count }}</option>\n                  </select>\n                </div>\n                <div class=\"passenger-group\">\n                  <svg class=\"passenger-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"/>\n                  </svg>\n                  <select formControlName=\"children\" class=\"passenger-select\">\n                    <option *ngFor=\"let count of passengerCounts.children\" [value]=\"count\">{{ count }}</option>\n                  </select>\n                </div>\n                <div class=\"passenger-group\">\n                  <svg class=\"passenger-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>\n                  </svg>\n                  <select formControlName=\"infants\" class=\"passenger-select\">\n                    <option *ngFor=\"let count of passengerCounts.infants\" [value]=\"count\">{{ count }}</option>\n                  </select>\n                </div>\n                <div class=\"class-group\">\n                  <svg class=\"class-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                  </svg>\n                  <select formControlName=\"flightClass\" class=\"class-select\">\n                    <option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">{{ flightClass.label }}</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"airline-field\">\n            <label class=\"airline-label\">Preferred Airline</label>\n            <select formControlName=\"preferredAirline\" class=\"airline-select\">\n              <option value=\"\">Preferred Airline</option>\n              <option *ngFor=\"let airline of preferredAirlines\" [value]=\"airline\">{{ airline }}</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Options Row -->\n        <div class=\"options-row\">\n          <div class=\"option-group\">\n            <label class=\"option-label\">Refundable fares</label>\n            <select class=\"option-select\">\n              <option>--All--</option>\n            </select>\n          </div>\n\n          <div class=\"option-group\">\n            <label class=\"option-label\">Baggage</label>\n            <select class=\"option-select\">\n              <option>--All--</option>\n            </select>\n          </div>\n\n          <div class=\"option-group\">\n            <label class=\"option-label\">Calendar</label>\n            <div class=\"calendar-display\">{{ getDaysBetweenDates() || '+/- 3 Days' }}</div>\n          </div>\n        </div>\n\n        <!-- Search Button -->\n        <button type=\"submit\" \n                class=\"search-button\"\n                [disabled]=\"isLoading || searchForm.invalid\"\n                [class.loading]=\"isLoading\">\n          <span *ngIf=\"!isLoading\">SEARCH NOW</span>\n          <span *ngIf=\"isLoading\" class=\"loading-content\">\n            <svg class=\"loading-spinner\" viewBox=\"0 0 24 24\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\" fill=\"none\" opacity=\"0.25\"></circle>\n              <path fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" opacity=\"0.75\"></path>\n            </svg>\n            Searching...\n          </span>\n        </button>\n      </form>\n    </div>\n\n    <!-- Latest Searches Sidebar -->\n    <div class=\"latest-searches-sidebar\">\n      <div class=\"latest-searches-content\">\n        <!-- Contenu des dernières recherches -->\n        <p class=\"no-searches\">No recent searches</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAA2BC,WAAW,QAAQ,sCAAsC;;;;;;;;;;ICmB9EC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,cAAA,EAAgE;IAAhEF,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAG,SAAA,eAA2K;IAC7KH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAiE;IAAvBD,EAAA,CAAAO,UAAA,mBAAAC,8DAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,OAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC9Dd,EAAA,CAAAE,cAAA,EAA6C;IAA7CF,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAG,SAAA,eAA4P;IAC9PH,EAAA,CAAAI,YAAA,EAAM;;;;IAJFJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAmDpBlB,EAAA,CAAAK,eAAA,EAAuH;IAAvHL,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,2BACF;;;;;;IA4BArB,EAAA,CAAAK,eAAA,EAAmH;IAAnHL,EAAA,CAAAC,cAAA,cAAmH;IACjHD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,yBACF;;;;;IAYArB,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAI,MAAA,CAAAF,eAAA,uBACF;;;;;IASArB,EAAA,CAAAC,cAAA,cAAyG;IACvGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAK,OAAA,CAAAH,eAAA,oBACF;;;;;IARFrB,EAAA,CAAAC,cAAA,cAA+E;IACnDD,EAAA,CAAAM,MAAA,SAAE;IAAAN,EAAA,CAAAI,YAAA,EAAQ;IACpCJ,EAAA,CAAAG,SAAA,gBAG0D;IAC1DH,EAAA,CAAAyB,UAAA,IAAAC,2CAAA,kBAEM;IACR1B,EAAA,CAAAI,YAAA,EAAM;;;;IAJGJ,EAAA,CAAAe,SAAA,GAAkD;IAAlDf,EAAA,CAAA2B,WAAA,UAAAC,MAAA,CAAAC,QAAA,2BAAkD;IACnD7B,EAAA,CAAAe,SAAA,GAA6E;IAA7Ef,EAAA,CAAA8B,UAAA,SAAAF,MAAA,CAAAG,YAAA,eAAAC,OAAA,IAAAJ,MAAA,CAAAG,YAAA,eAAAE,MAAA,CAA6E;;;;;IAiB3EjC,EAAA,CAAAC,cAAA,iBAAqE;IAAAD,EAAA,CAAAM,MAAA,GAAW;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAApCJ,EAAA,CAAA8B,UAAA,UAAAI,SAAA,CAAe;IAAClC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAkB,SAAA,CAAW;;;;;IAQhFlC,EAAA,CAAAC,cAAA,iBAAuE;IAAAD,EAAA,CAAAM,MAAA,GAAW;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAApCJ,EAAA,CAAA8B,UAAA,UAAAK,SAAA,CAAe;IAACnC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAmB,SAAA,CAAW;;;;;IAQlFnC,EAAA,CAAAC,cAAA,iBAAsE;IAAAD,EAAA,CAAAM,MAAA,GAAW;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAApCJ,EAAA,CAAA8B,UAAA,UAAAM,SAAA,CAAe;IAACpC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAoB,SAAA,CAAW;;;;;IAQjFpC,EAAA,CAAAC,cAAA,iBAA8E;IAAAD,EAAA,CAAAM,MAAA,GAAuB;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAA5DJ,EAAA,CAAA8B,UAAA,UAAAO,eAAA,CAAAC,KAAA,CAA2B;IAACtC,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAgB,iBAAA,CAAAqB,eAAA,CAAAE,KAAA,CAAuB;;;;;IAW3GvC,EAAA,CAAAC,cAAA,iBAAoE;IAAAD,EAAA,CAAAM,MAAA,GAAa;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAAxCJ,EAAA,CAAA8B,UAAA,UAAAU,WAAA,CAAiB;IAACxC,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,iBAAA,CAAAwB,WAAA,CAAa;;;;;IAgCrFxC,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAM,MAAA,iBAAU;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IAC1CJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAiD;IAAjDF,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,iBAA0G;IAE5GH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADpNjB,OAAM,MAAOqC,qBAAqB;EAgChCC,YACUC,WAAwB,EACxBC,aAA4B,EAC5BC,mBAAwC,EACxCC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IAlChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAA7B,YAAY,GAAG,EAAE;IAGjB;IACA,KAAA8B,aAAa,GAAG,CACd;MAAEV,KAAK,EAAEvC,WAAW,CAACkD,OAAO;MAAEV,KAAK,EAAE;IAAS,CAAE,EAChD;MAAED,KAAK,EAAEvC,WAAW,CAACmD,eAAe;MAAEX,KAAK,EAAE;IAAiB,CAAE,EAChE;MAAED,KAAK,EAAEvC,WAAW,CAACoD,QAAQ;MAAEZ,KAAK,EAAE;IAAU,CAAE,EAClD;MAAED,KAAK,EAAEvC,WAAW,CAACqD,KAAK;MAAEb,KAAK,EAAE;IAAa,CAAE,CACnD;IAED;IACA,KAAAc,eAAe,GAAG;MAChBC,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAACC,MAAM,EAAE;MAAC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;MAChDC,QAAQ,EAAEL,KAAK,CAACC,IAAI,CAAC;QAACC,MAAM,EAAE;MAAC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;MAC9CE,OAAO,EAAEN,KAAK,CAACC,IAAI,CAAC;QAACC,MAAM,EAAE;MAAC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC;KAC7C;IAED;IACA,KAAAG,iBAAiB,GAAG,CAClB,mBAAmB,EACnB,kBAAkB,EAClB,UAAU,EACV,eAAe,EACf,WAAW,EACX,YAAY,EACZ,iBAAiB,CAClB;EAOE;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;;EAIQD,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAACnB,mBAAmB,CAACqB,sBAAsB,EAAE;IAEjD;IACA;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACtB,mBAAmB,CAACuB,SAAS,EAAE;EACpD;EAEA;;;EAGQH,cAAcA,CAAA;IACpB,IAAI,CAACI,UAAU,GAAG,IAAI,CAAC1B,WAAW,CAAC2B,KAAK,CAAC;MACvCC,QAAQ,EAAE,CAAC,QAAQ,EAAEzE,UAAU,CAAC0E,QAAQ,CAAC;MACzCC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC3E,UAAU,CAAC0E,QAAQ,EAAE1E,UAAU,CAAC4E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAAC0E,QAAQ,EAAE1E,UAAU,CAAC4E,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACrEE,aAAa,EAAE,CAAC,EAAE,EAAE9E,UAAU,CAAC0E,QAAQ,CAAC;MACxCK,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBvB,MAAM,EAAE,CAAC,CAAC,EAAE,CAACxD,UAAU,CAAC0E,QAAQ,EAAE1E,UAAU,CAACgF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDlB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC9D,UAAU,CAAC0E,QAAQ,EAAE1E,UAAU,CAACgF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDjB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC/D,UAAU,CAAC0E,QAAQ,EAAE1E,UAAU,CAACgF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACtDC,WAAW,EAAE,CAAChF,WAAW,CAACkD,OAAO,EAAEnD,UAAU,CAAC0E,QAAQ,CAAC;MACvDQ,iBAAiB,EAAE,CAAC,KAAK,CAAC;MAC1BC,gBAAgB,EAAE,CAAC,EAAE;KACtB,CAAC;IAEF;IACA,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAC,UAAU,CAAC,EAAEC,YAAY,CAACC,SAAS,CAACb,QAAQ,IAAG;MACjE,MAAMc,iBAAiB,GAAG,IAAI,CAAChB,UAAU,CAACa,GAAG,CAAC,YAAY,CAAC;MAC3D,IAAIX,QAAQ,KAAK,WAAW,EAAE;QAC5Bc,iBAAiB,EAAEC,aAAa,CAAC,CAACxF,UAAU,CAAC0E,QAAQ,CAAC,CAAC;OACxD,MAAM;QACLa,iBAAiB,EAAEE,eAAe,EAAE;;MAEtCF,iBAAiB,EAAEG,sBAAsB,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEA;;;EAGA,IAAIzD,YAAYA,CAAA;IACd,OAAO,IAAI,CAACsC,UAAU,CAACoB,QAAQ;EACjC;EAEA;;;EAGAC,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAG,IAAI,CAACtB,UAAU,CAACa,GAAG,CAAC,mBAAmB,CAAC,EAAE5C,KAAK;IACjE,MAAMsD,OAAO,GAAG,IAAI,CAACvB,UAAU,CAACa,GAAG,CAAC,iBAAiB,CAAC,EAAE5C,KAAK;IAE7D,IAAI,CAAC+B,UAAU,CAACwB,UAAU,CAAC;MACzBpB,iBAAiB,EAAEmB,OAAO;MAC1BjB,eAAe,EAAEgB;KAClB,CAAC;EACJ;EAEA;;;EAGAG,kBAAkBA,CAAA;IAChB,MAAMxC,MAAM,GAAG,IAAI,CAACe,UAAU,CAACa,GAAG,CAAC,QAAQ,CAAC,EAAE5C,KAAK,IAAI,CAAC;IACxD,MAAMsB,QAAQ,GAAG,IAAI,CAACS,UAAU,CAACa,GAAG,CAAC,UAAU,CAAC,EAAE5C,KAAK,IAAI,CAAC;IAC5D,MAAMuB,OAAO,GAAG,IAAI,CAACQ,UAAU,CAACa,GAAG,CAAC,SAAS,CAAC,EAAE5C,KAAK,IAAI,CAAC;IAC1D,OAAOgB,MAAM,GAAGM,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGAkC,gBAAgBA,CAAA;IACd,MAAMzC,MAAM,GAAG,IAAI,CAACe,UAAU,CAACa,GAAG,CAAC,QAAQ,CAAC,EAAE5C,KAAK,IAAI,CAAC;IACxD,MAAMsB,QAAQ,GAAG,IAAI,CAACS,UAAU,CAACa,GAAG,CAAC,UAAU,CAAC,EAAE5C,KAAK,IAAI,CAAC;IAC5D,MAAMuB,OAAO,GAAG,IAAI,CAACQ,UAAU,CAACa,GAAG,CAAC,SAAS,CAAC,EAAE5C,KAAK,IAAI,CAAC;IAE1D,IAAI0D,IAAI,GAAG,GAAG1C,MAAM,SAASA,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IACpD,IAAIM,QAAQ,GAAG,CAAC,EAAEoC,IAAI,IAAI,KAAKpC,QAAQ,SAASA,QAAQ,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE;IAC3E,IAAIC,OAAO,GAAG,CAAC,EAAEmC,IAAI,IAAI,KAAKnC,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAEvE,OAAOmC,IAAI;EACb;EAEA;;;EAGAC,mBAAmBA,CAAA;IACjB,MAAMrB,aAAa,GAAG,IAAI,CAACP,UAAU,CAACa,GAAG,CAAC,eAAe,CAAC,EAAE5C,KAAK;IACjE,MAAMuC,UAAU,GAAG,IAAI,CAACR,UAAU,CAACa,GAAG,CAAC,YAAY,CAAC,EAAE5C,KAAK;IAE3D,IAAI,CAACsC,aAAa,IAAI,CAACC,UAAU,EAAE,OAAO,EAAE;IAE5C,MAAMqB,KAAK,GAAG,IAAIC,IAAI,CAACvB,aAAa,CAAC;IACrC,MAAMwB,GAAG,GAAG,IAAID,IAAI,CAACtB,UAAU,CAAC;IAChC,MAAMwB,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACI,OAAO,EAAE,GAAGN,KAAK,CAACM,OAAO,EAAE,CAAC;IAC1D,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAO,OAAOI,QAAQ,OAAO;EAC/B;EAEA;;;EAGAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtC,UAAU,CAACuC,KAAK,IAAI,CAAC,IAAI,CAAC7D,SAAS,EAAE;MAC5C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC7B,YAAY,GAAG,EAAE;MAEtB,MAAM2F,QAAQ,GAAG;QACftC,QAAQ,EAAE,IAAI,CAACF,UAAU,CAAC/B,KAAK,CAACiC,QAAQ;QACxCE,iBAAiB,EAAE,IAAI,CAACJ,UAAU,CAAC/B,KAAK,CAACmC,iBAAiB,CAACqC,IAAI,EAAE;QACjEnC,eAAe,EAAE,IAAI,CAACN,UAAU,CAAC/B,KAAK,CAACqC,eAAe,CAACmC,IAAI,EAAE;QAC7DlC,aAAa,EAAE,IAAI,CAACP,UAAU,CAAC/B,KAAK,CAACsC,aAAa;QAClDC,UAAU,EAAE,IAAI,CAACR,UAAU,CAAC/B,KAAK,CAACuC,UAAU;QAC5CkC,UAAU,EAAE;UACVzD,MAAM,EAAE,IAAI,CAACe,UAAU,CAAC/B,KAAK,CAACgB,MAAM;UACpCM,QAAQ,EAAE,IAAI,CAACS,UAAU,CAAC/B,KAAK,CAACsB,QAAQ;UACxCC,OAAO,EAAE,IAAI,CAACQ,UAAU,CAAC/B,KAAK,CAACuB;SAChC;QACDkB,WAAW,EAAE,IAAI,CAACV,UAAU,CAAC/B,KAAK,CAACyC,WAAW;QAC9CC,iBAAiB,EAAE,IAAI,CAACX,UAAU,CAAC/B,KAAK,CAAC0C,iBAAiB;QAC1DC,gBAAgB,EAAE,IAAI,CAACZ,UAAU,CAAC/B,KAAK,CAAC2C;OACzC;MAED,IAAI;QACF;QACA,MAAM+B,cAAc,GAAG,IAAI,CAACnE,mBAAmB,CAACoE,gBAAgB,CAACJ,QAAQ,EAAEA,QAAQ,CAACtC,QAAQ,CAAC;QAC7F,MAAM2C,QAAQ,GAAG,IAAI,CAACrE,mBAAmB,CAACsE,WAAW,CAACN,QAAQ,CAACtC,QAAQ,CAAC;QAExE6C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEL,cAAc,CAAC;QACvDI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEH,QAAQ,CAAC;QAE1C;QACA,IAAII,gBAAgB;QACpB,QAAQT,QAAQ,CAACtC,QAAQ;UACvB,KAAK,QAAQ;YACX+C,gBAAgB,GAAG,IAAI,CAAC1E,aAAa,CAAC2E,iCAAiC,CAACP,cAAc,EAAEE,QAAQ,CAAC;YACjG;UACF,KAAK,WAAW;YACdI,gBAAgB,GAAG,IAAI,CAAC1E,aAAa,CAAC4E,oCAAoC,CAACR,cAAc,EAAEE,QAAQ,CAAC;YACpG;UACF,KAAK,WAAW;YACdI,gBAAgB,GAAG,IAAI,CAAC1E,aAAa,CAAC6E,oCAAoC,CAACT,cAAc,EAAEE,QAAQ,CAAC;YACpG;UACF;YACE,IAAI,CAACnE,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC7B,YAAY,GAAG,2BAA2B;YAC/C;;QAGJoG,gBAAgB,CAAClC,SAAS,CAAC;UACzBsC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAC5E,SAAS,GAAG,KAAK;YACtB,IAAI4E,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;cAC9CT,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,QAAQ,CAAC;cAC3C;cACA;aACD,MAAM;cACL,IAAI,CAACG,cAAc,CAACH,QAAQ,CAAC;;UAEjC,CAAC;UACDI,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAAChF,SAAS,GAAG,KAAK;YACtB,IAAI,CAAC7B,YAAY,GAAG6G,KAAK,CAACC,OAAO,IAAI,8CAA8C;YACnFZ,OAAO,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC9C;SACD,CAAC;OACH,CAAC,OAAOA,KAAU,EAAE;QACnB,IAAI,CAAChF,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC7B,YAAY,GAAG6G,KAAK,CAACC,OAAO,IAAI,yBAAyB;QAC9DZ,OAAO,CAACW,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;KAE7C,MAAM;MACL,IAAI,CAACE,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQH,cAAcA,CAACH,QAAa;IAClC,IAAIA,QAAQ,CAACC,MAAM,CAACM,QAAQ,IAAIP,QAAQ,CAACC,MAAM,CAACM,QAAQ,CAACzE,MAAM,GAAG,CAAC,EAAE;MACnE,IAAI,CAACvC,YAAY,GAAGyG,QAAQ,CAACC,MAAM,CAACM,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;KACxD,MAAM;MACL,IAAI,CAAC9G,YAAY,GAAG,oCAAoC;;EAE5D;EAEA;;;EAGQ+G,oBAAoBA,CAAA;IAC1BE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/D,UAAU,CAACoB,QAAQ,CAAC,CAAC4C,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAClE,UAAU,CAACa,GAAG,CAACoD,GAAG,CAAC;MACxCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGA3G,QAAQA,CAAC4G,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAACtE,UAAU,CAACa,GAAG,CAACuD,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEE,KAAK,EAAE9G,QAAQ,CAAC6G,SAAS,CAAC,IAAIC,KAAK,EAAE3G,OAAO,CAAC;EACzD;EAEA;;;EAGAX,eAAeA,CAACoH,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAACtE,UAAU,CAACa,GAAG,CAACuD,SAAS,CAAC;IAE5C,IAAIE,KAAK,EAAE9G,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,qBAAqB;;IAG9B,IAAI8G,KAAK,EAAE9G,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,MAAM+G,cAAc,GAAGD,KAAK,CAAC1G,MAAM,GAAG,WAAW,CAAC,EAAE2G,cAAc;MAClE,OAAO,WAAWA,cAAc,oBAAoB;;IAGtD,IAAID,KAAK,EAAE9G,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1B,MAAMiD,GAAG,GAAG6D,KAAK,CAAC1G,MAAM,GAAG,KAAK,CAAC,EAAE6C,GAAG;MACtC,OAAO,yBAAyBA,GAAG,EAAE;;IAGvC,OAAO,EAAE;EACX;EAEA;;;EAGAhE,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBAvTWuB,qBAAqB,EAAAzC,EAAA,CAAA6I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/I,EAAA,CAAA6I,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAjJ,EAAA,CAAA6I,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAAnJ,EAAA,CAAA6I,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArB5G,qBAAqB;MAAA6G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlC5J,EAAA,CAAAC,cAAA,aAAqC;UAK7BD,EAAA,CAAAE,cAAA,EAA6C;UAA7CF,EAAA,CAAAC,cAAA,aAA6C;UAC3CD,EAAA,CAAAG,SAAA,gBAAgC;UAClCH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAkC;UAAlCL,EAAA,CAAAC,cAAA,aAAkC;UACPD,EAAA,CAAAM,MAAA,8BAAuB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAM,MAAA,iDAAyC;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI5EJ,EAAA,CAAAC,cAAA,cAA6B;UACFD,EAAA,CAAAM,MAAA,uBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAM,MAAA,iDAAyC;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAK5EJ,EAAA,CAAAC,cAAA,eAA4B;UAGxBD,EAAA,CAAAyB,UAAA,KAAAqI,qCAAA,kBAUM;UAEN9J,EAAA,CAAAC,cAAA,gBAAsF;UAAvDD,EAAA,CAAAO,UAAA,sBAAAwJ,yDAAA;YAAA,OAAYF,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAGpD3G,EAAA,CAAAC,cAAA,eAA4B;UAIlBD,EAAA,CAAAO,UAAA,mBAAAyJ,wDAAA;YAAA,OAASH,GAAA,CAAAxF,UAAA,CAAAwB,UAAA;cAAAtB,QAAA,EAAiC;YAAQ,EAAE;UAAA,EAAC;UAC3DvE,EAAA,CAAAM,MAAA,iBACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAGiE;UAAzDD,EAAA,CAAAO,UAAA,mBAAA0J,wDAAA;YAAA,OAASJ,GAAA,CAAAxF,UAAA,CAAAwB,UAAA;cAAAtB,QAAA,EAAiC;YAAW,EAAE;UAAA,EAAC;UAC9DvE,EAAA,CAAAM,MAAA,oBACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAGiE;UAAzDD,EAAA,CAAAO,UAAA,mBAAA2J,wDAAA;YAAA,OAASL,GAAA,CAAAxF,UAAA,CAAAwB,UAAA;cAAAtB,QAAA,EAAiC;YAAW,EAAE;UAAA,EAAC;UAC9DvE,EAAA,CAAAM,MAAA,+BACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAC,cAAA,eAA0B;UAEQD,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC1CJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,cAAA,EAAiF;UAAjFF,EAAA,CAAAC,cAAA,eAAiF;UAC/ED,EAAA,CAAAG,SAAA,gBAA4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAI4C;UAJ5CL,EAAA,CAAAG,SAAA,iBAI4C;UAC5CH,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA0F;UAC5FH,EAAA,CAAAI,YAAA,EAAM;UAGVJ,EAAA,CAAAyB,UAAA,KAAA0I,qCAAA,kBAEM;UACRnK,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAK,eAAA,EAAmE;UAAnEL,EAAA,CAAAC,cAAA,kBAAmE;UAAzBD,EAAA,CAAAO,UAAA,mBAAA6J,wDAAA;YAAA,OAASP,GAAA,CAAAnE,YAAA,EAAc;UAAA,EAAC;UAChE1F,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA4H;UAC9HH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAK,eAAA,EAA4B;UAA5BL,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAM,MAAA,UAAE;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACxCJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,cAAA,EAAiF;UAAjFF,EAAA,CAAAC,cAAA,eAAiF;UAC/ED,EAAA,CAAAG,SAAA,gBAA8J;UAEhKH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAIyC;UAJzCL,EAAA,CAAAG,SAAA,iBAIyC;UACzCH,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA0F;UAC5FH,EAAA,CAAAI,YAAA,EAAM;UAGVJ,EAAA,CAAAyB,UAAA,KAAA4I,qCAAA,kBAEM;UACRrK,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAK,eAAA,EAAsB;UAAtBL,EAAA,CAAAC,cAAA,eAAsB;UAEQD,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAG,SAAA,iBAG6D;UAC7DH,EAAA,CAAAyB,UAAA,KAAA6I,qCAAA,kBAEM;UACRtK,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAyB,UAAA,KAAA8I,qCAAA,kBASM;UACRvK,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAiC;UAEED,EAAA,CAAAM,MAAA,mCAA2B;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAClEJ,EAAA,CAAAC,cAAA,eAA+B;UAGzBD,EAAA,CAAAE,cAAA,EAAkF;UAAlFF,EAAA,CAAAC,cAAA,eAAkF;UAChFD,EAAA,CAAAG,SAAA,gBAA+I;UACjJH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA0D;UAA1DL,EAAA,CAAAC,cAAA,kBAA0D;UACxDD,EAAA,CAAAyB,UAAA,KAAA+I,wCAAA,qBAAyF;UAC3FxK,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAAkF;UAAlFF,EAAA,CAAAC,cAAA,eAAkF;UAChFD,EAAA,CAAAG,SAAA,gBAAkV;UACpVH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA4D;UAA5DL,EAAA,CAAAC,cAAA,kBAA4D;UAC1DD,EAAA,CAAAyB,UAAA,KAAAgJ,wCAAA,qBAA2F;UAC7FzK,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAAkF;UAAlFF,EAAA,CAAAC,cAAA,eAAkF;UAChFD,EAAA,CAAAG,SAAA,gBAAuM;UACzMH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA2D;UAA3DL,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAyB,UAAA,KAAAiJ,wCAAA,qBAA0F;UAC5F1K,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAuH;UACzHH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA2D;UAA3DL,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAyB,UAAA,KAAAkJ,wCAAA,qBAA8G;UAChH3K,EAAA,CAAAI,YAAA,EAAS;UAMjBJ,EAAA,CAAAC,cAAA,eAA2B;UACID,EAAA,CAAAM,MAAA,yBAAiB;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACtDJ,EAAA,CAAAC,cAAA,kBAAkE;UAC/CD,EAAA,CAAAM,MAAA,yBAAiB;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAC3CJ,EAAA,CAAAyB,UAAA,KAAAmJ,wCAAA,qBAA0F;UAC5F5K,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAC,cAAA,eAAyB;UAEOD,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACpDJ,EAAA,CAAAC,cAAA,kBAA8B;UACpBD,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAI5BJ,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC3CJ,EAAA,CAAAC,cAAA,mBAA8B;UACpBD,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAI5BJ,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAM,MAAA,iBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC5CJ,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAM,MAAA,KAA2C;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAKnFJ,EAAA,CAAAC,cAAA,mBAGoC;UAClCD,EAAA,CAAAyB,UAAA,MAAAoJ,uCAAA,mBAA0C;UAC1C7K,EAAA,CAAAyB,UAAA,MAAAqJ,uCAAA,mBAMO;UACT9K,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAC,cAAA,gBAAqC;UAGVD,EAAA,CAAAM,MAAA,2BAAkB;UAAAN,EAAA,CAAAI,YAAA,EAAI;;;UAlNzCJ,EAAA,CAAAe,SAAA,IAAkB;UAAlBf,EAAA,CAAA8B,UAAA,SAAA+H,GAAA,CAAA3I,YAAA,CAAkB;UAYlBlB,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAA8B,UAAA,cAAA+H,GAAA,CAAAxF,UAAA,CAAwB;UAMlBrE,EAAA,CAAAe,SAAA,GAA4D;UAA5Df,EAAA,CAAA2B,WAAA,WAAAkI,GAAA,CAAA9H,YAAA,aAAAO,KAAA,cAA4D;UAM5DtC,EAAA,CAAAe,SAAA,GAA+D;UAA/Df,EAAA,CAAA2B,WAAA,WAAAkI,GAAA,CAAA9H,YAAA,aAAAO,KAAA,iBAA+D;UAM/DtC,EAAA,CAAAe,SAAA,GAA+D;UAA/Df,EAAA,CAAA2B,WAAA,WAAAkI,GAAA,CAAA9H,YAAA,aAAAO,KAAA,iBAA+D;UAiB5DtC,EAAA,CAAAe,SAAA,GAAuG;UAAvGf,EAAA,CAAA2B,WAAA,UAAAkI,GAAA,CAAAhI,QAAA,qCAAAgI,GAAA,CAAAhI,QAAA,mCAAuG;UAQ1G7B,EAAA,CAAAe,SAAA,GAA2F;UAA3Ff,EAAA,CAAA8B,UAAA,SAAA+H,GAAA,CAAA9H,YAAA,sBAAAC,OAAA,IAAA6H,GAAA,CAAA9H,YAAA,sBAAAE,MAAA,CAA2F;UAsBxFjC,EAAA,CAAAe,SAAA,IAAmG;UAAnGf,EAAA,CAAA2B,WAAA,UAAAkI,GAAA,CAAAhI,QAAA,mCAAAgI,GAAA,CAAAhI,QAAA,iCAAmG;UAQtG7B,EAAA,CAAAe,SAAA,GAAuF;UAAvFf,EAAA,CAAA8B,UAAA,SAAA+H,GAAA,CAAA9H,YAAA,oBAAAC,OAAA,IAAA6H,GAAA,CAAA9H,YAAA,oBAAAE,MAAA,CAAuF;UAatFjC,EAAA,CAAAe,SAAA,GAAqD;UAArDf,EAAA,CAAA2B,WAAA,UAAAkI,GAAA,CAAAhI,QAAA,8BAAqD;UACtD7B,EAAA,CAAAe,SAAA,GAAmF;UAAnFf,EAAA,CAAA8B,UAAA,SAAA+H,GAAA,CAAA9H,YAAA,kBAAAC,OAAA,IAAA6H,GAAA,CAAA9H,YAAA,kBAAAE,MAAA,CAAmF;UAKlEjC,EAAA,CAAAe,SAAA,GAAoD;UAApDf,EAAA,CAAA8B,UAAA,SAAA+H,GAAA,CAAA9H,YAAA,aAAAO,KAAA,iBAAoD;UAuBzCtC,EAAA,CAAAe,SAAA,IAAyB;UAAzBf,EAAA,CAAA8B,UAAA,YAAA+H,GAAA,CAAAxG,eAAA,CAAAC,MAAA,CAAyB;UAQzBtD,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAA8B,UAAA,YAAA+H,GAAA,CAAAxG,eAAA,CAAAO,QAAA,CAA2B;UAQ3B5D,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAA8B,UAAA,YAAA+H,GAAA,CAAAxG,eAAA,CAAAQ,OAAA,CAA0B;UAQpB7D,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA8B,UAAA,YAAA+H,GAAA,CAAA7G,aAAA,CAAgB;UAW1BhD,EAAA,CAAAe,SAAA,GAAoB;UAApBf,EAAA,CAAA8B,UAAA,YAAA+H,GAAA,CAAA/F,iBAAA,CAAoB;UAuBpB9D,EAAA,CAAAe,SAAA,IAA2C;UAA3Cf,EAAA,CAAAgB,iBAAA,CAAA6I,GAAA,CAAA5D,mBAAA,mBAA2C;UAQrEjG,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAA2B,WAAA,YAAAkI,GAAA,CAAA9G,SAAA,CAA2B;UAD3B/C,EAAA,CAAA8B,UAAA,aAAA+H,GAAA,CAAA9G,SAAA,IAAA8G,GAAA,CAAAxF,UAAA,CAAA0G,OAAA,CAA4C;UAE3C/K,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA8B,UAAA,UAAA+H,GAAA,CAAA9G,SAAA,CAAgB;UAChB/C,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAA8B,UAAA,SAAA+H,GAAA,CAAA9G,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}