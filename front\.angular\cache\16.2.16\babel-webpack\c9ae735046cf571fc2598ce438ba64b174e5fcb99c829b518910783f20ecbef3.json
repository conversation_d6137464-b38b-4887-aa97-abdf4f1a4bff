{"ast": null, "code": "/**\n * Configuration pour mapper les champs du frontend vers les modèles backend\n * Permet d'adapter facilement le formulaire à vos modèles exacts\n */\n// Configuration par défaut basée sur vos modèles actuels\nexport const DEFAULT_FIELD_CONFIG = {\n  // Champs de base\n  departureLocationField: 'DepartureLocations',\n  arrivalLocationField: 'ArrivalLocations',\n  departureDateField: 'CheckIn',\n  returnDateField: 'CheckOut',\n  // Passagers\n  passengersField: 'Passengers',\n  adultPassengerType: 1,\n  childPassengerType: 2,\n  infantPassengerType: 3,\n  // Configuration\n  productTypeField: 'ProductType',\n  productTypeValue: 2,\n  serviceTypesField: 'ServiceTypes',\n  serviceTypesValue: ['1'],\n  // Optionnels\n  flightClassField: 'flightClasses',\n  directFlightsField: 'showOnlyNonStopFlight',\n  cultureField: 'Culture',\n  currencyField: 'Currency',\n  // Spécifiques\n  nightsField: 'Night',\n  checkInsField: 'checkIns'\n};\nexport const DEFAULT_LABELS_CONFIG = {\n  // Labels\n  departureLabel: 'From',\n  arrivalLabel: 'To',\n  departureDateLabel: 'From',\n  returnDateLabel: 'To',\n  passengersLabel: 'Passenger & Class of travel',\n  flightClassLabel: 'Class',\n  preferredAirlineLabel: 'Preferred Airline',\n  // Placeholders\n  departurePlaceholder: 'IST - Istanbul Airport',\n  arrivalPlaceholder: 'TUN - Carthage Arpt',\n  // Options\n  directFlightsLabel: 'Direct flights only',\n  refundableFaresLabel: 'Refundable fares',\n  baggageLabel: 'Baggage',\n  calendarLabel: 'Calendar',\n  // Boutons\n  searchButtonText: 'SEARCH NOW',\n  swapButtonTooltip: 'Swap airports'\n};\nexport const DEFAULT_SEARCH_CONFIG = {\n  fieldMapping: DEFAULT_FIELD_CONFIG,\n  labels: DEFAULT_LABELS_CONFIG,\n  // Options\n  enableDirectFlights: true,\n  enablePreferredAirline: true,\n  enableRefundableFares: true,\n  enableBaggageOptions: true,\n  enableCalendarOptions: true,\n  // Validation\n  minLocationLength: 3,\n  maxPassengers: 9,\n  // API\n  endpoints: {\n    oneWay: '/api/flights/search/oneway',\n    roundTrip: '/api/flights/search/roundtrip',\n    multiCity: '/api/flights/search/multicity'\n  }\n};\n// Utilitaire pour mapper les données du formulaire vers votre backend\nexport class FormToBackendMapper {\n  static mapToOneWayRequest(formData, config) {\n    const request = {};\n    // Mapping des champs de base\n    request[config.productTypeField] = config.productTypeValue;\n    request[config.serviceTypesField] = config.serviceTypesValue;\n    request[config.departureDateField] = formData.departureDate;\n    // Locations\n    request[config.departureLocationField] = [{\n      id: formData.departureLocation,\n      type: 1 // Airport par défaut\n    }];\n\n    request[config.arrivalLocationField] = [{\n      id: formData.arrivalLocation,\n      type: 1\n    }];\n    // Passagers\n    request[config.passengersField] = this.buildPassengers(formData.passengers, config);\n    // Options\n    if (config.directFlightsField) {\n      request[config.directFlightsField] = formData.directFlightsOnly || false;\n    }\n    if (config.flightClassField && formData.flightClass) {\n      request[config.flightClassField] = [formData.flightClass];\n    }\n    if (config.cultureField) {\n      request[config.cultureField] = 'en-US';\n    }\n    if (config.currencyField) {\n      request[config.currencyField] = 'USD';\n    }\n    return request;\n  }\n  static mapToRoundTripRequest(formData, config) {\n    const request = this.mapToOneWayRequest(formData, config);\n    // Ajouter les champs spécifiques au round trip\n    if (config.nightsField && formData.returnDate) {\n      const nights = this.calculateNights(formData.departureDate, formData.returnDate);\n      request[config.nightsField] = nights;\n    }\n    return request;\n  }\n  static mapToMultiCityRequest(formData, config) {\n    const request = {};\n    // Adapter selon votre structure MultiCity\n    request[config.productTypeField.toLowerCase()] = config.productTypeValue;\n    request[config.serviceTypesField.toLowerCase()] = config.serviceTypesValue;\n    if (config.checkInsField) {\n      request[config.checkInsField] = [formData.departureDate];\n    }\n    return request;\n  }\n  static buildPassengers(passengers, config) {\n    const passengerList = [];\n    if (passengers.adults > 0) {\n      passengerList.push({\n        type: config.adultPassengerType,\n        count: passengers.adults\n      });\n    }\n    if (passengers.children > 0) {\n      passengerList.push({\n        type: config.childPassengerType,\n        count: passengers.children\n      });\n    }\n    if (passengers.infants > 0) {\n      passengerList.push({\n        type: config.infantPassengerType,\n        count: passengers.infants\n      });\n    }\n    return passengerList;\n  }\n  static calculateNights(checkIn, checkOut) {\n    const startDate = new Date(checkIn);\n    const endDate = new Date(checkOut);\n    const timeDiff = endDate.getTime() - startDate.getTime();\n    return Math.ceil(timeDiff / (1000 * 3600 * 24));\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_FIELD_CONFIG", "departureLocationField", "arrivalLocationField", "departureDateField", "returnDateField", "passengersField", "adultPassengerType", "childPassengerType", "infantPassengerType", "productTypeField", "productTypeValue", "serviceTypesField", "serviceTypesValue", "flightClassField", "directFlightsField", "cultureField", "currencyField", "nightsField", "checkInsField", "DEFAULT_LABELS_CONFIG", "departure<PERSON><PERSON><PERSON>", "arrival<PERSON><PERSON><PERSON>", "departureDateLabel", "returnDateLabel", "passengersLabel", "flightClassLabel", "preferredAirlineLabel", "departurePlaceholder", "arrivalPlaceholder", "directFlightsLabel", "refundableFaresLabel", "baggageLabel", "calendarLabel", "searchButtonText", "swapButtonTooltip", "DEFAULT_SEARCH_CONFIG", "fieldMapping", "labels", "enableDirectFlights", "enablePreferredAirline", "enableRefundableFares", "enableBaggageOptions", "enableCalendarOptions", "minLocationLength", "max<PERSON><PERSON><PERSON><PERSON>", "endpoints", "oneWay", "roundTrip", "multiCity", "FormToBackendMapper", "mapToOneWayRequest", "formData", "config", "request", "departureDate", "id", "departureLocation", "type", "arrivalLocation", "buildPassengers", "passengers", "directFlightsOnly", "flightClass", "mapToRoundTripRequest", "returnDate", "nights", "calculateNights", "mapToMultiCityRequest", "toLowerCase", "passengerList", "adults", "push", "count", "children", "infants", "checkIn", "checkOut", "startDate", "Date", "endDate", "timeDiff", "getTime", "Math", "ceil"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\models\\backend-field-mapping.interface.ts"], "sourcesContent": ["/**\n * Configuration pour mapper les champs du frontend vers les modèles backend\n * Permet d'adapter facilement le formulaire à vos modèles exacts\n */\n\n// Configuration des champs du formulaire\nexport interface FormFieldConfig {\n  // Champs de base\n  departureLocationField: string;\n  arrivalLocationField: string;\n  departureDateField: string;\n  returnDateField?: string;\n  \n  // Champs de passagers\n  passengersField: string;\n  adultPassengerType: number;\n  childPassengerType: number;\n  infantPassengerType: number;\n  \n  // Champs de configuration\n  productTypeField: string;\n  productTypeValue: number;\n  serviceTypesField: string;\n  serviceTypesValue: string[];\n  \n  // Champs optionnels\n  flightClassField?: string;\n  directFlightsField?: string;\n  cultureField?: string;\n  currencyField?: string;\n  \n  // Champs spécifiques par type de voyage\n  nightsField?: string; // Pour RoundTrip\n  checkInsField?: string; // Pour MultiCity\n}\n\n// Configuration par défaut basée sur vos modèles actuels\nexport const DEFAULT_FIELD_CONFIG: FormFieldConfig = {\n  // Champs de base\n  departureLocationField: 'DepartureLocations',\n  arrivalLocationField: 'ArrivalLocations', \n  departureDateField: 'CheckIn',\n  returnDateField: 'CheckOut',\n  \n  // Passagers\n  passengersField: 'Passengers',\n  adultPassengerType: 1,\n  childPassengerType: 2,\n  infantPassengerType: 3,\n  \n  // Configuration\n  productTypeField: 'ProductType',\n  productTypeValue: 2,\n  serviceTypesField: 'ServiceTypes',\n  serviceTypesValue: ['1'],\n  \n  // Optionnels\n  flightClassField: 'flightClasses',\n  directFlightsField: 'showOnlyNonStopFlight',\n  cultureField: 'Culture',\n  currencyField: 'Currency',\n  \n  // Spécifiques\n  nightsField: 'Night',\n  checkInsField: 'checkIns'\n};\n\n// Interface pour personnaliser les labels et placeholders\nexport interface FormLabelsConfig {\n  // Labels des champs\n  departureLabel: string;\n  arrivalLabel: string;\n  departureDateLabel: string;\n  returnDateLabel: string;\n  passengersLabel: string;\n  flightClassLabel: string;\n  preferredAirlineLabel: string;\n  \n  // Placeholders\n  departurePlaceholder: string;\n  arrivalPlaceholder: string;\n  \n  // Options\n  directFlightsLabel: string;\n  refundableFaresLabel: string;\n  baggageLabel: string;\n  calendarLabel: string;\n  \n  // Boutons\n  searchButtonText: string;\n  swapButtonTooltip: string;\n}\n\nexport const DEFAULT_LABELS_CONFIG: FormLabelsConfig = {\n  // Labels\n  departureLabel: 'From',\n  arrivalLabel: 'To', \n  departureDateLabel: 'From',\n  returnDateLabel: 'To',\n  passengersLabel: 'Passenger & Class of travel',\n  flightClassLabel: 'Class',\n  preferredAirlineLabel: 'Preferred Airline',\n  \n  // Placeholders\n  departurePlaceholder: 'IST - Istanbul Airport',\n  arrivalPlaceholder: 'TUN - Carthage Arpt',\n  \n  // Options\n  directFlightsLabel: 'Direct flights only',\n  refundableFaresLabel: 'Refundable fares',\n  baggageLabel: 'Baggage',\n  calendarLabel: 'Calendar',\n  \n  // Boutons\n  searchButtonText: 'SEARCH NOW',\n  swapButtonTooltip: 'Swap airports'\n};\n\n// Interface pour la configuration complète du composant\nexport interface FlightSearchConfig {\n  fieldMapping: FormFieldConfig;\n  labels: FormLabelsConfig;\n  \n  // Options de comportement\n  enableDirectFlights: boolean;\n  enablePreferredAirline: boolean;\n  enableRefundableFares: boolean;\n  enableBaggageOptions: boolean;\n  enableCalendarOptions: boolean;\n  \n  // Validation\n  minLocationLength: number;\n  maxPassengers: number;\n  \n  // API\n  endpoints: {\n    oneWay: string;\n    roundTrip: string;\n    multiCity: string;\n  };\n}\n\nexport const DEFAULT_SEARCH_CONFIG: FlightSearchConfig = {\n  fieldMapping: DEFAULT_FIELD_CONFIG,\n  labels: DEFAULT_LABELS_CONFIG,\n  \n  // Options\n  enableDirectFlights: true,\n  enablePreferredAirline: true,\n  enableRefundableFares: true,\n  enableBaggageOptions: true,\n  enableCalendarOptions: true,\n  \n  // Validation\n  minLocationLength: 3,\n  maxPassengers: 9,\n  \n  // API\n  endpoints: {\n    oneWay: '/api/flights/search/oneway',\n    roundTrip: '/api/flights/search/roundtrip',\n    multiCity: '/api/flights/search/multicity'\n  }\n};\n\n// Interface pour les données du formulaire adaptées à votre backend\nexport interface BackendFlightRequest {\n  [key: string]: any; // Permet d'adapter dynamiquement aux champs de votre backend\n}\n\n// Utilitaire pour mapper les données du formulaire vers votre backend\nexport class FormToBackendMapper {\n  \n  static mapToOneWayRequest(formData: any, config: FormFieldConfig): BackendFlightRequest {\n    const request: BackendFlightRequest = {};\n    \n    // Mapping des champs de base\n    request[config.productTypeField] = config.productTypeValue;\n    request[config.serviceTypesField] = config.serviceTypesValue;\n    request[config.departureDateField] = formData.departureDate;\n    \n    // Locations\n    request[config.departureLocationField] = [{\n      id: formData.departureLocation,\n      type: 1 // Airport par défaut\n    }];\n    request[config.arrivalLocationField] = [{\n      id: formData.arrivalLocation,\n      type: 1\n    }];\n    \n    // Passagers\n    request[config.passengersField] = this.buildPassengers(formData.passengers, config);\n    \n    // Options\n    if (config.directFlightsField) {\n      request[config.directFlightsField] = formData.directFlightsOnly || false;\n    }\n    \n    if (config.flightClassField && formData.flightClass) {\n      request[config.flightClassField] = [formData.flightClass];\n    }\n    \n    if (config.cultureField) {\n      request[config.cultureField] = 'en-US';\n    }\n    \n    if (config.currencyField) {\n      request[config.currencyField] = 'USD';\n    }\n    \n    return request;\n  }\n  \n  static mapToRoundTripRequest(formData: any, config: FormFieldConfig): BackendFlightRequest {\n    const request = this.mapToOneWayRequest(formData, config);\n    \n    // Ajouter les champs spécifiques au round trip\n    if (config.nightsField && formData.returnDate) {\n      const nights = this.calculateNights(formData.departureDate, formData.returnDate);\n      request[config.nightsField] = nights;\n    }\n    \n    return request;\n  }\n  \n  static mapToMultiCityRequest(formData: any, config: FormFieldConfig): BackendFlightRequest {\n    const request: BackendFlightRequest = {};\n    \n    // Adapter selon votre structure MultiCity\n    request[config.productTypeField.toLowerCase()] = config.productTypeValue;\n    request[config.serviceTypesField.toLowerCase()] = config.serviceTypesValue;\n    \n    if (config.checkInsField) {\n      request[config.checkInsField] = [formData.departureDate];\n    }\n    \n    return request;\n  }\n  \n  private static buildPassengers(passengers: any, config: FormFieldConfig) {\n    const passengerList = [];\n    \n    if (passengers.adults > 0) {\n      passengerList.push({\n        type: config.adultPassengerType,\n        count: passengers.adults\n      });\n    }\n    \n    if (passengers.children > 0) {\n      passengerList.push({\n        type: config.childPassengerType,\n        count: passengers.children\n      });\n    }\n    \n    if (passengers.infants > 0) {\n      passengerList.push({\n        type: config.infantPassengerType,\n        count: passengers.infants\n      });\n    }\n    \n    return passengerList;\n  }\n  \n  private static calculateNights(checkIn: string, checkOut: string): number {\n    const startDate = new Date(checkIn);\n    const endDate = new Date(checkOut);\n    const timeDiff = endDate.getTime() - startDate.getTime();\n    return Math.ceil(timeDiff / (1000 * 3600 * 24));\n  }\n}\n"], "mappings": "AAAA;;;;AAoCA;AACA,OAAO,MAAMA,oBAAoB,GAAoB;EACnD;EACAC,sBAAsB,EAAE,oBAAoB;EAC5CC,oBAAoB,EAAE,kBAAkB;EACxCC,kBAAkB,EAAE,SAAS;EAC7BC,eAAe,EAAE,UAAU;EAE3B;EACAC,eAAe,EAAE,YAAY;EAC7BC,kBAAkB,EAAE,CAAC;EACrBC,kBAAkB,EAAE,CAAC;EACrBC,mBAAmB,EAAE,CAAC;EAEtB;EACAC,gBAAgB,EAAE,aAAa;EAC/BC,gBAAgB,EAAE,CAAC;EACnBC,iBAAiB,EAAE,cAAc;EACjCC,iBAAiB,EAAE,CAAC,GAAG,CAAC;EAExB;EACAC,gBAAgB,EAAE,eAAe;EACjCC,kBAAkB,EAAE,uBAAuB;EAC3CC,YAAY,EAAE,SAAS;EACvBC,aAAa,EAAE,UAAU;EAEzB;EACAC,WAAW,EAAE,OAAO;EACpBC,aAAa,EAAE;CAChB;AA4BD,OAAO,MAAMC,qBAAqB,GAAqB;EACrD;EACAC,cAAc,EAAE,MAAM;EACtBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,MAAM;EAC1BC,eAAe,EAAE,IAAI;EACrBC,eAAe,EAAE,6BAA6B;EAC9CC,gBAAgB,EAAE,OAAO;EACzBC,qBAAqB,EAAE,mBAAmB;EAE1C;EACAC,oBAAoB,EAAE,wBAAwB;EAC9CC,kBAAkB,EAAE,qBAAqB;EAEzC;EACAC,kBAAkB,EAAE,qBAAqB;EACzCC,oBAAoB,EAAE,kBAAkB;EACxCC,YAAY,EAAE,SAAS;EACvBC,aAAa,EAAE,UAAU;EAEzB;EACAC,gBAAgB,EAAE,YAAY;EAC9BC,iBAAiB,EAAE;CACpB;AA0BD,OAAO,MAAMC,qBAAqB,GAAuB;EACvDC,YAAY,EAAEpC,oBAAoB;EAClCqC,MAAM,EAAElB,qBAAqB;EAE7B;EACAmB,mBAAmB,EAAE,IAAI;EACzBC,sBAAsB,EAAE,IAAI;EAC5BC,qBAAqB,EAAE,IAAI;EAC3BC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAE3B;EACAC,iBAAiB,EAAE,CAAC;EACpBC,aAAa,EAAE,CAAC;EAEhB;EACAC,SAAS,EAAE;IACTC,MAAM,EAAE,4BAA4B;IACpCC,SAAS,EAAE,+BAA+B;IAC1CC,SAAS,EAAE;;CAEd;AAOD;AACA,OAAM,MAAOC,mBAAmB;EAE9B,OAAOC,kBAAkBA,CAACC,QAAa,EAAEC,MAAuB;IAC9D,MAAMC,OAAO,GAAyB,EAAE;IAExC;IACAA,OAAO,CAACD,MAAM,CAAC3C,gBAAgB,CAAC,GAAG2C,MAAM,CAAC1C,gBAAgB;IAC1D2C,OAAO,CAACD,MAAM,CAACzC,iBAAiB,CAAC,GAAGyC,MAAM,CAACxC,iBAAiB;IAC5DyC,OAAO,CAACD,MAAM,CAACjD,kBAAkB,CAAC,GAAGgD,QAAQ,CAACG,aAAa;IAE3D;IACAD,OAAO,CAACD,MAAM,CAACnD,sBAAsB,CAAC,GAAG,CAAC;MACxCsD,EAAE,EAAEJ,QAAQ,CAACK,iBAAiB;MAC9BC,IAAI,EAAE,CAAC,CAAC;KACT,CAAC;;IACFJ,OAAO,CAACD,MAAM,CAAClD,oBAAoB,CAAC,GAAG,CAAC;MACtCqD,EAAE,EAAEJ,QAAQ,CAACO,eAAe;MAC5BD,IAAI,EAAE;KACP,CAAC;IAEF;IACAJ,OAAO,CAACD,MAAM,CAAC/C,eAAe,CAAC,GAAG,IAAI,CAACsD,eAAe,CAACR,QAAQ,CAACS,UAAU,EAAER,MAAM,CAAC;IAEnF;IACA,IAAIA,MAAM,CAACtC,kBAAkB,EAAE;MAC7BuC,OAAO,CAACD,MAAM,CAACtC,kBAAkB,CAAC,GAAGqC,QAAQ,CAACU,iBAAiB,IAAI,KAAK;;IAG1E,IAAIT,MAAM,CAACvC,gBAAgB,IAAIsC,QAAQ,CAACW,WAAW,EAAE;MACnDT,OAAO,CAACD,MAAM,CAACvC,gBAAgB,CAAC,GAAG,CAACsC,QAAQ,CAACW,WAAW,CAAC;;IAG3D,IAAIV,MAAM,CAACrC,YAAY,EAAE;MACvBsC,OAAO,CAACD,MAAM,CAACrC,YAAY,CAAC,GAAG,OAAO;;IAGxC,IAAIqC,MAAM,CAACpC,aAAa,EAAE;MACxBqC,OAAO,CAACD,MAAM,CAACpC,aAAa,CAAC,GAAG,KAAK;;IAGvC,OAAOqC,OAAO;EAChB;EAEA,OAAOU,qBAAqBA,CAACZ,QAAa,EAAEC,MAAuB;IACjE,MAAMC,OAAO,GAAG,IAAI,CAACH,kBAAkB,CAACC,QAAQ,EAAEC,MAAM,CAAC;IAEzD;IACA,IAAIA,MAAM,CAACnC,WAAW,IAAIkC,QAAQ,CAACa,UAAU,EAAE;MAC7C,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACf,QAAQ,CAACG,aAAa,EAAEH,QAAQ,CAACa,UAAU,CAAC;MAChFX,OAAO,CAACD,MAAM,CAACnC,WAAW,CAAC,GAAGgD,MAAM;;IAGtC,OAAOZ,OAAO;EAChB;EAEA,OAAOc,qBAAqBA,CAAChB,QAAa,EAAEC,MAAuB;IACjE,MAAMC,OAAO,GAAyB,EAAE;IAExC;IACAA,OAAO,CAACD,MAAM,CAAC3C,gBAAgB,CAAC2D,WAAW,EAAE,CAAC,GAAGhB,MAAM,CAAC1C,gBAAgB;IACxE2C,OAAO,CAACD,MAAM,CAACzC,iBAAiB,CAACyD,WAAW,EAAE,CAAC,GAAGhB,MAAM,CAACxC,iBAAiB;IAE1E,IAAIwC,MAAM,CAAClC,aAAa,EAAE;MACxBmC,OAAO,CAACD,MAAM,CAAClC,aAAa,CAAC,GAAG,CAACiC,QAAQ,CAACG,aAAa,CAAC;;IAG1D,OAAOD,OAAO;EAChB;EAEQ,OAAOM,eAAeA,CAACC,UAAe,EAAER,MAAuB;IACrE,MAAMiB,aAAa,GAAG,EAAE;IAExB,IAAIT,UAAU,CAACU,MAAM,GAAG,CAAC,EAAE;MACzBD,aAAa,CAACE,IAAI,CAAC;QACjBd,IAAI,EAAEL,MAAM,CAAC9C,kBAAkB;QAC/BkE,KAAK,EAAEZ,UAAU,CAACU;OACnB,CAAC;;IAGJ,IAAIV,UAAU,CAACa,QAAQ,GAAG,CAAC,EAAE;MAC3BJ,aAAa,CAACE,IAAI,CAAC;QACjBd,IAAI,EAAEL,MAAM,CAAC7C,kBAAkB;QAC/BiE,KAAK,EAAEZ,UAAU,CAACa;OACnB,CAAC;;IAGJ,IAAIb,UAAU,CAACc,OAAO,GAAG,CAAC,EAAE;MAC1BL,aAAa,CAACE,IAAI,CAAC;QACjBd,IAAI,EAAEL,MAAM,CAAC5C,mBAAmB;QAChCgE,KAAK,EAAEZ,UAAU,CAACc;OACnB,CAAC;;IAGJ,OAAOL,aAAa;EACtB;EAEQ,OAAOH,eAAeA,CAACS,OAAe,EAAEC,QAAgB;IAC9D,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACH,OAAO,CAAC;IACnC,MAAMI,OAAO,GAAG,IAAID,IAAI,CAACF,QAAQ,CAAC;IAClC,MAAMI,QAAQ,GAAGD,OAAO,CAACE,OAAO,EAAE,GAAGJ,SAAS,CAACI,OAAO,EAAE;IACxD,OAAOC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;EACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}