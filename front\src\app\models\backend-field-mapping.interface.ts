/**
 * Configuration pour mapper les champs du frontend vers les modèles backend
 * Permet d'adapter facilement le formulaire à vos modèles exacts
 */

// Configuration des champs du formulaire
export interface FormFieldConfig {
  // Champs de base
  departureLocationField: string;
  arrivalLocationField: string;
  departureDateField: string;
  returnDateField?: string;
  
  // Champs de passagers
  passengersField: string;
  adultPassengerType: number;
  childPassengerType: number;
  infantPassengerType: number;
  
  // Champs de configuration
  productTypeField: string;
  productTypeValue: number;
  serviceTypesField: string;
  serviceTypesValue: string[];
  
  // Champs optionnels
  flightClassField?: string;
  directFlightsField?: string;
  cultureField?: string;
  currencyField?: string;
  
  // Champs spécifiques par type de voyage
  nightsField?: string; // Pour RoundTrip
  checkInsField?: string; // Pour MultiCity
}

// Configuration par défaut basée sur vos modèles actuels
export const DEFAULT_FIELD_CONFIG: FormFieldConfig = {
  // Champs de base
  departureLocationField: 'DepartureLocations',
  arrivalLocationField: 'ArrivalLocations', 
  departureDateField: 'CheckIn',
  returnDateField: 'CheckOut',
  
  // Passagers
  passengersField: 'Passengers',
  adultPassengerType: 1,
  childPassengerType: 2,
  infantPassengerType: 3,
  
  // Configuration
  productTypeField: 'ProductType',
  productTypeValue: 2,
  serviceTypesField: 'ServiceTypes',
  serviceTypesValue: ['1'],
  
  // Optionnels
  flightClassField: 'flightClasses',
  directFlightsField: 'showOnlyNonStopFlight',
  cultureField: 'Culture',
  currencyField: 'Currency',
  
  // Spécifiques
  nightsField: 'Night',
  checkInsField: 'checkIns'
};

// Interface pour personnaliser les labels et placeholders
export interface FormLabelsConfig {
  // Labels des champs
  departureLabel: string;
  arrivalLabel: string;
  departureDateLabel: string;
  returnDateLabel: string;
  passengersLabel: string;
  flightClassLabel: string;
  preferredAirlineLabel: string;
  
  // Placeholders
  departurePlaceholder: string;
  arrivalPlaceholder: string;
  
  // Options
  directFlightsLabel: string;
  refundableFaresLabel: string;
  baggageLabel: string;
  calendarLabel: string;
  
  // Boutons
  searchButtonText: string;
  swapButtonTooltip: string;
}

export const DEFAULT_LABELS_CONFIG: FormLabelsConfig = {
  // Labels
  departureLabel: 'From',
  arrivalLabel: 'To', 
  departureDateLabel: 'From',
  returnDateLabel: 'To',
  passengersLabel: 'Passenger & Class of travel',
  flightClassLabel: 'Class',
  preferredAirlineLabel: 'Preferred Airline',
  
  // Placeholders
  departurePlaceholder: 'IST - Istanbul Airport',
  arrivalPlaceholder: 'TUN - Carthage Arpt',
  
  // Options
  directFlightsLabel: 'Direct flights only',
  refundableFaresLabel: 'Refundable fares',
  baggageLabel: 'Baggage',
  calendarLabel: 'Calendar',
  
  // Boutons
  searchButtonText: 'SEARCH NOW',
  swapButtonTooltip: 'Swap airports'
};

// Interface pour la configuration complète du composant
export interface FlightSearchConfig {
  fieldMapping: FormFieldConfig;
  labels: FormLabelsConfig;
  
  // Options de comportement
  enableDirectFlights: boolean;
  enablePreferredAirline: boolean;
  enableRefundableFares: boolean;
  enableBaggageOptions: boolean;
  enableCalendarOptions: boolean;
  
  // Validation
  minLocationLength: number;
  maxPassengers: number;
  
  // API
  endpoints: {
    oneWay: string;
    roundTrip: string;
    multiCity: string;
  };
}

export const DEFAULT_SEARCH_CONFIG: FlightSearchConfig = {
  fieldMapping: DEFAULT_FIELD_CONFIG,
  labels: DEFAULT_LABELS_CONFIG,
  
  // Options
  enableDirectFlights: true,
  enablePreferredAirline: true,
  enableRefundableFares: true,
  enableBaggageOptions: true,
  enableCalendarOptions: true,
  
  // Validation
  minLocationLength: 3,
  maxPassengers: 9,
  
  // API
  endpoints: {
    oneWay: '/api/flights/search/oneway',
    roundTrip: '/api/flights/search/roundtrip',
    multiCity: '/api/flights/search/multicity'
  }
};

// Interface pour les données du formulaire adaptées à votre backend
export interface BackendFlightRequest {
  [key: string]: any; // Permet d'adapter dynamiquement aux champs de votre backend
}

// Utilitaire pour mapper les données du formulaire vers votre backend
export class FormToBackendMapper {
  
  static mapToOneWayRequest(formData: any, config: FormFieldConfig): BackendFlightRequest {
    const request: BackendFlightRequest = {};
    
    // Mapping des champs de base
    request[config.productTypeField] = config.productTypeValue;
    request[config.serviceTypesField] = config.serviceTypesValue;
    request[config.departureDateField] = formData.departureDate;
    
    // Locations
    request[config.departureLocationField] = [{
      id: formData.departureLocation,
      type: 1 // Airport par défaut
    }];
    request[config.arrivalLocationField] = [{
      id: formData.arrivalLocation,
      type: 1
    }];
    
    // Passagers
    request[config.passengersField] = this.buildPassengers(formData.passengers, config);
    
    // Options
    if (config.directFlightsField) {
      request[config.directFlightsField] = formData.directFlightsOnly || false;
    }
    
    if (config.flightClassField && formData.flightClass) {
      request[config.flightClassField] = [formData.flightClass];
    }
    
    if (config.cultureField) {
      request[config.cultureField] = 'en-US';
    }
    
    if (config.currencyField) {
      request[config.currencyField] = 'USD';
    }
    
    return request;
  }
  
  static mapToRoundTripRequest(formData: any, config: FormFieldConfig): BackendFlightRequest {
    const request = this.mapToOneWayRequest(formData, config);
    
    // Ajouter les champs spécifiques au round trip
    if (config.nightsField && formData.returnDate) {
      const nights = this.calculateNights(formData.departureDate, formData.returnDate);
      request[config.nightsField] = nights;
    }
    
    return request;
  }
  
  static mapToMultiCityRequest(formData: any, config: FormFieldConfig): BackendFlightRequest {
    const request: BackendFlightRequest = {};
    
    // Adapter selon votre structure MultiCity
    request[config.productTypeField.toLowerCase()] = config.productTypeValue;
    request[config.serviceTypesField.toLowerCase()] = config.serviceTypesValue;
    
    if (config.checkInsField) {
      request[config.checkInsField] = [formData.departureDate];
    }
    
    return request;
  }
  
  private static buildPassengers(passengers: any, config: FormFieldConfig) {
    const passengerList = [];
    
    if (passengers.adults > 0) {
      passengerList.push({
        type: config.adultPassengerType,
        count: passengers.adults
      });
    }
    
    if (passengers.children > 0) {
      passengerList.push({
        type: config.childPassengerType,
        count: passengers.children
      });
    }
    
    if (passengers.infants > 0) {
      passengerList.push({
        type: config.infantPassengerType,
        count: passengers.infants
      });
    }
    
    return passengerList;
  }
  
  private static calculateNights(checkIn: string, checkOut: string): number {
    const startDate = new Date(checkIn);
    const endDate = new Date(checkOut);
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }
}
